<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('anyones', function (Blueprint $table) {
            // Ubah role enum untuk menambahkan 'none'
            $table->enum('role', ['none', 'elite', 'premium'])->default('none')->change();
            
            // Tambahkan payment fields
            $table->string('payment_status')->default('pending')->after('role'); // pending, paid, expired
            $table->string('midtrans_order_id')->nullable()->after('payment_status');
            $table->string('midtrans_transaction_id')->nullable()->after('midtrans_order_id');
            $table->decimal('payment_amount', 10, 2)->nullable()->after('midtrans_transaction_id');
            $table->timestamp('payment_date')->nullable()->after('payment_amount');
            $table->json('midtrans_response')->nullable()->after('payment_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('anyones', function (Blueprint $table) {
            // Hapus payment fields
            $table->dropColumn([
                'payment_status',
                'midtrans_order_id', 
                'midtrans_transaction_id',
                'payment_amount',
                'payment_date',
                'midtrans_response'
            ]);
            
            // Kembalikan role enum ke semula
            $table->enum('role', ['elite', 'premium'])->default('elite')->change();
        });
    }
};
