@echo off
setlocal

REM Encoder Tools - Batch Script untuk Windows
REM Script ini memudahkan penggunaan Encoder Tools tanpa perlu mengetik perintah node secara manual

echo Encoder Tools
echo ============================================

REM Periksa apakah Node.js terinstal
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
  echo Error: Node.js tidak ditemukan. Silakan instal Node.js terlebih dahulu.
  echo Kunjungi https://nodejs.org/ untuk mengunduh dan menginstal Node.js.
  exit /b 1
)

REM Periksa apakah ada argumen yang diberikan
if "%~1"=="" (
  echo Penggunaan: encode [perintah] [opsi]
  echo.
  echo Untuk melihat bantuan lengkap, ketik: encode help
  exit /b 0
)

REM Jalankan encoder tools dengan argumen yang diberikan
node "%~dp0index.js" %*

endlocal