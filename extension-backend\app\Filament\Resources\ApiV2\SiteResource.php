<?php

namespace App\Filament\Resources\ApiV2;

use App\Filament\Resources\ApiV2\SiteResource\Pages;
use App\Filament\Resources\ApiV2\SiteResource\RelationManagers;
use App\Models\Site;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SiteResource extends Resource
{
    protected static ?string $model = Site::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?string $navigationLabel = 'Situs Web V2';

    protected static ?string $modelLabel = 'Situs Web V2';

    protected static ?string $pluralModelLabel = 'Situs Web V2';

    protected static ?string $navigationGroup = 'API V2';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Dasar')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Situs')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('url')
                            ->label('URL Situs')
                            ->url()
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('domain')
                            ->label('Domain')
                            ->helperText('Domain situs (opsional, akan digunakan untuk filtering)')
                            ->maxLength(255),

                        Forms\Components\FileUpload::make('logo_path')
                            ->label('Logo Situs')
                            ->image()
                            ->directory('site-logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'])
                            ->maxSize(2048)
                            ->helperText('Upload logo situs (maksimal 2MB)'),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3)
                            ->maxLength(500),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true),

                        Forms\Components\Select::make('visibility')
                            ->label('Visibilitas Role')
                            ->options([
                                'both' => 'Semua Role',
                                'elite' => 'Elite Saja',
                                'premium' => 'Premium Saja',
                            ])
                            ->default('both')
                            ->required()
                            ->helperText('Tentukan role mana yang dapat mengakses situs ini'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('File JSON Cookies')
                    ->description('Upload file JSON yang berisi data cookies untuk situs ini')
                    ->schema([
                        Forms\Components\FileUpload::make('json_files')
                            ->label('File JSON Cookies')
                            ->acceptedFileTypes(['application/json', '.json'])
                            ->directory('json-files')
                            ->visibility('public')
                            ->maxSize(5120)
                            ->multiple()
                            ->reorderable()
                            ->downloadable()
                            ->deletable()
                            ->previewable(false)
                            ->helperText('Upload multiple file JSON berisi data cookies (maksimal 5MB per file)')
                            ->hint('Nama file akan digunakan sebagai identifier untuk setiap file JSON')
                            ->afterStateUpdated(function ($state, $old, $component) {
                                // This will be handled in the EditSite page
                            })
                    ]),

                Forms\Components\Section::make('Akun Alternatif')
                    ->description('Kelola akun alternatif untuk situs ini')
                    ->schema([
                        Forms\Components\Repeater::make('alternative_accounts')
                            ->label('Akun Alternatif')
                            ->schema([
                                Forms\Components\TextInput::make('account_name')
                                    ->label('Nama Akun')
                                    ->required(),

                                Forms\Components\TextInput::make('username')
                                    ->label('Username/Email')
                                    ->required(),

                                Forms\Components\TextInput::make('password')
                                    ->label('Password')
                                    ->password()
                                    ->required(),

                                Forms\Components\Textarea::make('custom_fields')
                                    ->label('Field Kustom (JSON)')
                                    ->helperText('Masukkan field kustom dalam format JSON')
                                    ->placeholder('{"field1": "value1", "field2": "value2"}')
                                    ->rows(3),

                                Forms\Components\Toggle::make('is_primary')
                                    ->label('Akun Utama')
                                    ->default(false),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('Aktif')
                                    ->default(true),
                            ])
                            ->columns(2)
                            ->collapsible()
                            ->addActionLabel('Tambah Akun'),
                    ]),

                Forms\Components\Section::make('Injeksi Akun Situs')
                    ->description('Konfigurasi untuk injeksi akun melalui path element')
                    ->schema([
                        Forms\Components\TextInput::make('login_url')
                            ->label('URL Login')
                            ->url()
                            ->helperText('URL halaman login situs'),

                        Forms\Components\TextInput::make('email_selector')
                            ->label('Selector Email')
                            ->helperText('CSS selector untuk field email (contoh: #email, input[name="email"])')
                            ->placeholder('#email'),

                        Forms\Components\TextInput::make('password_selector')
                            ->label('Selector Password')
                            ->helperText('CSS selector untuk field password')
                            ->placeholder('#password'),

                        Forms\Components\TextInput::make('submit_selector')
                            ->label('Selector Tombol Submit')
                            ->helperText('CSS selector untuk tombol submit login')
                            ->placeholder('button[type="submit"]'),

                        Forms\Components\Textarea::make('additional_script')
                            ->label('Script Tambahan')
                            ->helperText('JavaScript tambahan yang akan dijalankan setelah injeksi')
                            ->rows(4),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Pengalihan Situs Kustom')
                    ->description('Konfigurasi untuk halaman maintenance dan pengalihan')
                    ->schema([
                        Forms\Components\Toggle::make('enable_custom_redirect')
                            ->label('Aktifkan Pengalihan Kustom')
                            ->default(false)
                            ->live(),

                        Forms\Components\TextInput::make('redirect_title')
                            ->label('Judul Halaman Maintenance')
                            ->default('Situs Sedang Maintenance')
                            ->visible(fn (Forms\Get $get) => $get('enable_custom_redirect')),

                        Forms\Components\TextInput::make('redirect_content')
                            ->label('Konten Halaman Maintenance')
                            ->default('<p>Situs sedang dalam tahap pemeliharaan. Silakan coba lagi nanti.</p>')
                            ->visible(fn (Forms\Get $get) => $get('enable_custom_redirect')),

                        Forms\Components\TextInput::make('redirect_url')
                            ->label('URL Pengalihan')
                            ->url()
                            ->helperText('URL tujuan pengalihan (opsional)')
                            ->visible(fn (Forms\Get $get) => $get('enable_custom_redirect')),

                        Forms\Components\Select::make('redirect_delay')
                            ->label('Delay Pengalihan (detik)')
                            ->options([
                                0 => 'Langsung',
                                3 => '3 detik',
                                5 => '5 detik',
                                10 => '10 detik',
                                30 => '30 detik',
                            ])
                            ->default(5)
                            ->visible(fn (Forms\Get $get) => $get('enable_custom_redirect')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('logo_path')
                    ->label('Logo')
                    ->circular()
                    ->size(40)
                    ->defaultImageUrl('/images/default-site-logo.png'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Situs')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('url')
                    ->label('URL')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('domain')
                    ->label('Domain')
                    ->searchable()
                    ->placeholder('Tidak ada domain'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean(),

                Tables\Columns\TextColumn::make('visibility')
                    ->label('Visibilitas')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'both' => 'Semua Role',
                        'elite' => 'Elite Saja',
                        'premium' => 'Premium Saja',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'both' => 'success',
                        'elite' => 'warning',
                        'premium' => 'info',
                        default => 'secondary',
                    }),

                Tables\Columns\TextColumn::make('json_files_count')
                    ->label('File JSON')
                    ->getStateUsing(fn ($record) => $record->jsonFiles()->count())
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('accounts_count')
                    ->label('Akun')
                    ->getStateUsing(fn ($record) => count($record->alternative_accounts ?? []))
                    ->badge()
                    ->color('success'),

                Tables\Columns\IconColumn::make('enable_custom_redirect')
                    ->label('Redirect')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),

                Tables\Filters\TernaryFilter::make('enable_custom_redirect')
                    ->label('Pengalihan Aktif'),

                Tables\Filters\SelectFilter::make('visibility')
                    ->label('Visibilitas Role')
                    ->options([
                        'both' => 'Semua Role',
                        'elite' => 'Elite Saja',
                        'premium' => 'Premium Saja',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\JsonFilesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSites::route('/'),
            'create' => Pages\CreateSite::route('/create'),
            'view' => Pages\ViewSite::route('/{record}'),
            'edit' => Pages\EditSite::route('/{record}/edit'),
        ];
    }
}
