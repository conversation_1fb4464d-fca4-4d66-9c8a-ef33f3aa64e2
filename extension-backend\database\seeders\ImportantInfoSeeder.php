<?php

namespace Database\Seeders;

use App\Models\ImportantInfo;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ImportantInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ImportantInfo::create([
            'title' => 'Selamat Datang di Satu Pintu v2',
            'content' => 'Versi baru dengan fitur halaman detail untuk situs dengan multiple cookies. Klik pada situs untuk melihat detail dan pilih cookie yang ingin digunakan.',
            'type' => 'info',
            'is_active' => true,
            'start_date' => now(),
            'end_date' => now()->addDays(30),
        ]);

        ImportantInfo::create([
            'title' => 'Pemeliharaan Sistem',
            'content' => 'Sistem akan mengalami pemeliharaan pada tanggal 20 Agustus 2023 pukul 00:00 - 02:00 WIB. Mohon maaf atas ketidaknyamanannya.',
            'type' => 'warning',
            'is_active' => false,
            'start_date' => now()->addDays(5),
            'end_date' => now()->addDays(6),
        ]);
    }
}