{"name": "Encoder Tools", "version": "1.0.0", "description": "Alat untuk melakukan encoding pada JSON, HTML, dan <PERSON>", "features": ["JSON encoding", "HTML encoding", "JavaScript encoding", "File encoding", "Folder encoding"], "config": {"outputFolder": "./output", "supportedExtensions": [".json", ".html", ".htm", ".js"]}, "examples": {"json": "node index.js encode-json '{\"name\":\"<PERSON>\"}'\n", "html": "node index.js encode-html '<div>Hello</div>'", "javascript": "node index.js encode-js 'function test() { return true; }'"}, "specialChars": "!@#$%^&*()_+{}[]|:;<>,.?/~`'"}