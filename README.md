# Satu Pintu - Cookie Manager Extension

Sistem manajemen cookie terintegrasi yang terdiri dari browser extension dan backend Laravel dengan panel admin Filament.

## 🚀 Fitur Utama

### Backend Laravel
- **Panel Admin Filament** untuk mengelola situs dan pengguna
- **Sistem Role** (Elite & Premium) dengan akses berbeda
- **API Authentication** menggunakan Laravel Sanctum
- **Database SQLite** untuk kemudahan development
- **Web Login** untuk autentikasi pengguna extension

### Browser Extension
- **Cookie Management** otomatis berdasarkan situs
- **Role-based Access** (Elite hanya akses situs Elite, Premium akses semua)
- **Integration** dengan backend Laravel
- **Modern UI** dengan popup yang user-friendly

## 📁 Struktur Proyek

```
extension-browser/
├── extension/                 # Browser Extension
│   ├── manifest.json         # Extension manifest
│   ├── popup.html            # Extension popup UI
│   ├── js/
│   │   ├── popup.js          # Popup functionality
│   │   ├── background.js     # Background script
│   │   └── content.js        # Content script
│   └── cookies.json          # Cookie storage
│
└── extension-backend/        # Laravel Backend
    ├── app/
    │   ├── Filament/         # Admin panel resources
    │   ├── Http/Controllers/ # API & Web controllers
    │   └── Models/           # Database models
    ├── database/
    │   ├── migrations/       # Database migrations
    │   └── seeders/          # Sample data
    ├── routes/
    │   ├── api.php           # API routes
    │   └── web.php           # Web routes
    └── resources/views/      # Blade templates
```

## 🛠️ Setup & Installation

### Backend Laravel

1. **Masuk ke direktori backend:**
   ```bash
   cd extension-backend
   ```

2. **Install dependencies:**
   ```bash
   composer install
   ```

3. **Setup environment:**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Jalankan migrasi dan seeder:**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Jalankan server:**
   ```bash
   php artisan serve
   ```

### Browser Extension

1. **Buka Chrome/Edge dan masuk ke Extensions:**
   - Chrome: `chrome://extensions/`
   - Edge: `edge://extensions/`

2. **Enable Developer Mode**

3. **Load Unpacked Extension:**
   - Klik "Load unpacked"
   - Pilih folder `extension/`

## 🔑 Akun Demo

### Admin Panel (Filament)
- **URL:** http://127.0.0.1:8000/admin
- **Email:** <EMAIL>
- **Password:** (yang Anda set saat setup)

### Pengguna Elite
- **Email:** <EMAIL>
- **Password:** password
- **Akses:** Hanya situs kategori Elite

### Pengguna Premium
- **Email:** <EMAIL>
- **Password:** password
- **Akses:** Semua situs (Elite + Premium)

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/login` - Login pengguna
- `POST /api/auth/logout` - Logout pengguna
- `GET /api/auth/me` - Info pengguna login

### Sites
- `GET /api/sites` - Daftar situs berdasarkan role
- `GET /api/sites/{id}` - Detail situs dengan cookies
- `POST /api/sites/cookies` - Get cookies untuk domain

## 🎯 Cara Penggunaan

### 1. Setup Admin
1. Login ke panel admin Filament
2. Tambahkan situs-situs di menu "Sites"
3. Upload thumbnail dan set kategori (Elite/Premium)
4. Tambahkan cookies untuk setiap situs
5. Kelola pengguna di menu "Pengguna Elite/Premium"

### 2. Penggunaan Extension
1. Install extension di browser
2. Klik icon extension
3. Login dengan akun Elite/Premium
4. Pilih situs dari daftar
5. Cookies akan otomatis diterapkan

### 3. Flow Autentikasi
1. User klik "Login" di extension
2. Diarahkan ke halaman web login
3. Setelah login, dapat token API
4. Token digunakan untuk akses API dari extension

## 🔧 Konfigurasi

### Environment Variables (.env)
```env
APP_NAME="Satu Pintu"
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite
```

### Extension Configuration
- Update `YOUR_EXTENSION_ID` di AuthController
- Sesuaikan API base URL di extension

## 📊 Database Schema

### Users (Admin)
- id, name, email, password, timestamps

### Anyones (Elite/Premium Users)
- id, name, email, password, role, is_active, timestamps

### Sites
- id, name, url, domain, category, thumbnail, description, cookies, is_active, timestamps

## 🚀 Development

### Menambah Situs Baru
1. Login ke admin panel
2. Buka menu "Sites"
3. Klik "Create"
4. Isi informasi situs dan cookies
5. Set kategori sesuai target user

### Menambah User Baru
1. Login ke admin panel
2. Buka menu "Pengguna Elite/Premium"
3. Klik "Create"
4. Isi informasi user dan pilih role

## 🔒 Security

- **API Authentication** menggunakan Sanctum tokens
- **Role-based Access Control** untuk situs
- **Password Hashing** untuk semua user
- **CSRF Protection** untuk web forms

## 🐛 Troubleshooting

### Extension tidak bisa connect ke API
- Pastikan server Laravel berjalan
- Check CORS settings
- Verify API endpoints

### Login gagal
- Check credentials di database
- Verify user is_active = true
- Check browser console untuk errors

### Cookies tidak tersimpan
- Check extension permissions
- Verify cookie format di database
- Check domain matching

## 📝 TODO / Future Enhancements

- [ ] Cookie encryption untuk security
- [ ] Bulk cookie import/export
- [ ] User activity logging
- [ ] Cookie expiration management
- [ ] Multi-browser support
- [ ] Real-time sync antar devices
- [ ] Advanced cookie filtering
- [ ] Integration dengan password managers

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - feel free to use for personal or commercial projects.