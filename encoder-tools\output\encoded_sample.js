%2F**%0A%20*%20sample.js%20-%20Contoh%20file%20JavaScript%20untuk%20Encoder%20Tools%0A%20*%20%0A%20*%20File%20ini%20berisi%20berbagai%20contoh%20kode%20JavaScript%20yang%20dapat%20digunakan%0A%20*%20untuk%20menguji%20fungsionalitas%20encoding%20dari%20Encoder%20Tools.%0A%20*%2F%0A%0A%2F%2F%20Deklarasi%20variabel%20dengan%20berbagai%20tipe%20data%0Aconst%20appName%20%3D%20'Encoder%20Tools'%3B%0Aconst%20version%20%3D%20'1.0.0'%3B%0Aconst%20isActive%20%3D%20true%3B%0Aconst%20features%20%3D%20%5B'JSON'%2C%20'HTML'%2C%20'JavaScript'%2C%20'File'%2C%20'Folder'%5D%3B%0Aconst%20specialChars%20%3D%20'!%40%23%24%25%5E%26*()_%2B%7B%7D%5B%5D%7C%3A%3B%22%5C'%5C%5C%5C'%3C%3E%2C.%3F%2F~%60'%3B%0A%0A%2F%2F%20Objek%20konfigurasi%0Aconst%20config%20%3D%20%7B%0A%20%20outputFolder%3A%20'.%2Foutput'%2C%0A%20%20supportedExtensions%3A%20%5B'.json'%2C%20'.html'%2C%20'.htm'%2C%20'.js'%5D%2C%0A%20%20encoding%3A%20'UTF-8'%2C%0A%20%20options%3A%20%7B%0A%20%20%20%20prettyPrint%3A%20false%2C%0A%20%20%20%20saveToFile%3A%20true%2C%0A%20%20%20%20logToConsole%3A%20true%0A%20%20%7D%0A%7D%3B%0A%0A%2F**%0A%20*%20Fungsi%20untuk%20menampilkan%20informasi%20tentang%20aplikasi%0A%20*%20%40returns%20%7Bstring%7D%20Informasi%20aplikasi%0A%20*%2F%0Afunction%20getAppInfo()%20%7B%0A%20%20return%20%60%24%7BappName%7D%20v%24%7Bversion%7D%60%3B%0A%7D%0A%0A%2F**%0A%20*%20Fungsi%20untuk%20memeriksa%20apakah%20ekstensi%20file%20didukung%0A%20*%20%40param%20%7Bstring%7D%20filename%20-%20Nama%20file%20yang%20akan%20diperiksa%0A%20*%20%40returns%20%7Bboolean%7D%20True%20jika%20ekstensi%20didukung%2C%20false%20jika%20tidak%0A%20*%2F%0Afunction%20isSupportedExtension(filename)%20%7B%0A%20%20const%20ext%20%3D%20filename.substring(filename.lastIndexOf('.'))%3B%0A%20%20return%20config.supportedExtensions.includes(ext)%3B%0A%7D%0A%0A%2F**%0A%20*%20Kelas%20untuk%20mengelola%20proses%20encoding%0A%20*%2F%0Aclass%20Encoder%20%7B%0A%20%20constructor(type)%20%7B%0A%20%20%20%20this.type%20%3D%20type%3B%0A%20%20%20%20this.initialized%20%3D%20false%3B%0A%20%20%7D%0A%0A%20%20initialize()%20%7B%0A%20%20%20%20console.log(%60Initializing%20%24%7Bthis.type%7D%20encoder...%60)%3B%0A%20%20%20%20this.initialized%20%3D%20true%3B%0A%20%20%7D%0A%0A%20%20encode(input)%20%7B%0A%20%20%20%20if%20(!this.initialized)%20%7B%0A%20%20%20%20%20%20this.initialize()%3B%0A%20%20%20%20%7D%0A%0A%20%20%20%20console.log(%60Encoding%20%24%7Bthis.type%7D...%60)%3B%0A%20%20%20%20%0A%20%20%20%20%2F%2F%20Contoh%20implementasi%20sederhana%0A%20%20%20%20switch(this.type)%20%7B%0A%20%20%20%20%20%20case%20'json'%3A%0A%20%20%20%20%20%20%20%20return%20encodeURIComponent(JSON.stringify(input))%3B%0A%20%20%20%20%20%20case%20'html'%3A%0A%20%20%20%20%20%20%20%20return%20input%0A%20%20%20%20%20%20%20%20%20%20.replace(%2F%26%2Fg%2C%20'%26amp%3B')%0A%20%20%20%20%20%20%20%20%20%20.replace(%2F%3C%2Fg%2C%20'%26lt%3B')%0A%20%20%20%20%20%20%20%20%20%20.replace(%2F%3E%2Fg%2C%20'%26gt%3B')%0A%20%20%20%20%20%20%20%20%20%20.replace(%2F%22%2Fg%2C%20'%26quot%3B')%0A%20%20%20%20%20%20%20%20%20%20.replace(%2F'%2Fg%2C%20'%26%2339%3B')%3B%0A%20%20%20%20%20%20case%20'javascript'%3A%0A%20%20%20%20%20%20%20%20return%20encodeURIComponent(input)%3B%0A%20%20%20%20%20%20default%3A%0A%20%20%20%20%20%20%20%20throw%20new%20Error(%60Unsupported%20encoding%20type%3A%20%24%7Bthis.type%7D%60)%3B%0A%20%20%20%20%7D%0A%20%20%7D%0A%7D%0A%0A%2F%2F%20Contoh%20penggunaan%20arrow%20function%0Aconst%20logResult%20%3D%20(result)%20%3D%3E%20%7B%0A%20%20console.log('Hasil%20encoding%3A')%3B%0A%20%20console.log(result)%3B%0A%7D%3B%0A%0A%2F%2F%20Contoh%20penggunaan%20Promise%0Afunction%20encodeAsync(input%2C%20type)%20%7B%0A%20%20return%20new%20Promise((resolve%2C%20reject)%20%3D%3E%20%7B%0A%20%20%20%20try%20%7B%0A%20%20%20%20%20%20const%20encoder%20%3D%20new%20Encoder(type)%3B%0A%20%20%20%20%20%20const%20result%20%3D%20encoder.encode(input)%3B%0A%20%20%20%20%20%20setTimeout(()%20%3D%3E%20%7B%0A%20%20%20%20%20%20%20%20resolve(result)%3B%0A%20%20%20%20%20%20%7D%2C%20100)%3B%0A%20%20%20%20%7D%20catch%20(error)%20%7B%0A%20%20%20%20%20%20reject(error)%3B%0A%20%20%20%20%7D%0A%20%20%7D)%3B%0A%7D%0A%0A%2F%2F%20Contoh%20penggunaan%20async%2Fawait%0Aasync%20function%20processEncoding()%20%7B%0A%20%20try%20%7B%0A%20%20%20%20const%20jsonResult%20%3D%20await%20encodeAsync(%7B%20name%3A%20'Test'%20%7D%2C%20'json')%3B%0A%20%20%20%20const%20htmlResult%20%3D%20await%20encodeAsync('%3Cdiv%3EHello%3C%2Fdiv%3E'%2C%20'html')%3B%0A%20%20%20%20const%20jsResult%20%3D%20await%20encodeAsync('function%20test()%20%7B%20return%20true%3B%20%7D'%2C%20'javascript')%3B%0A%20%20%20%20%0A%20%20%20%20return%20%7B%0A%20%20%20%20%20%20json%3A%20jsonResult%2C%0A%20%20%20%20%20%20html%3A%20htmlResult%2C%0A%20%20%20%20%20%20javascript%3A%20jsResult%0A%20%20%20%20%7D%3B%0A%20%20%7D%20catch%20(error)%20%7B%0A%20%20%20%20console.error('Error%20during%20encoding%3A'%2C%20error)%3B%0A%20%20%20%20return%20null%3B%0A%20%20%7D%0A%7D%0A%0A%2F%2F%20Contoh%20penggunaan%20template%20literals%0Aconst%20generateReport%20%3D%20(results)%20%3D%3E%20%60%0AEncoding%20Report%3A%0A----------------%0AJSON%3A%20%24%7Bresults.json%7D%0AHTML%3A%20%24%7Bresults.html%7D%0AJavaScript%3A%20%24%7Bresults.javascript%7D%0A----------------%0AGenerated%20at%3A%20%24%7Bnew%20Date().toISOString()%7D%0A%60%3B%0A%0A%2F%2F%20Export%20modul%0Amodule.exports%20%3D%20%7B%0A%20%20getAppInfo%2C%0A%20%20isSupportedExtension%2C%0A%20%20Encoder%2C%0A%20%20encodeAsync%2C%0A%20%20processEncoding%2C%0A%20%20generateReport%2C%0A%20%20config%0A%7D%3B%0A%0A%2F%2F%20Contoh%20kode%20yang%20akan%20dijalankan%20jika%20file%20ini%20dieksekusi%20langsung%0Aif%20(require.main%20%3D%3D%3D%20module)%20%7B%0A%20%20console.log(getAppInfo())%3B%0A%20%20console.log('Supported%20extensions%3A'%2C%20config.supportedExtensions)%3B%0A%20%20%0A%20%20const%20encoder%20%3D%20new%20Encoder('json')%3B%0A%20%20const%20result%20%3D%20encoder.encode(%7B%20test%3A%20'Hello%20World'%20%7D)%3B%0A%20%20logResult(result)%3B%0A%20%20%0A%20%20processEncoding().then(results%20%3D%3E%20%7B%0A%20%20%20%20if%20(results)%20%7B%0A%20%20%20%20%20%20console.log(generateReport(results))%3B%0A%20%20%20%20%7D%0A%20%20%7D)%3B%0A%7D