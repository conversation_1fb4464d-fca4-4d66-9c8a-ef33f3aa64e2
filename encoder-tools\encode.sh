#!/bin/bash

# Encoder Tools - Shell Script untuk Linux/macOS
# Script ini memudahkan penggunaan Encoder Tools tanpa perlu mengetik perintah node secara manual

echo "Encoder Tools"
echo "============================================"

# Periksa apakah Node.js terinstal
if ! command -v node &> /dev/null; then
  echo "Error: Node.js tidak ditemukan. Silakan instal Node.js terlebih dahulu."
  echo "Kunjungi https://nodejs.org/ untuk mengunduh dan menginstal Node.js."
  exit 1
 fi

# Dapatkan direktori script saat ini
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Periksa apakah ada argumen yang diberikan
if [ $# -eq 0 ]; then
  echo "Penggunaan: ./encode.sh [perintah] [opsi]"
  echo ""
  echo "Untuk melihat bantuan lengkap, ketik: ./encode.sh help"
  exit 0
fi

# Jalankan encoder tools dengan argumen yang diberikan
node "$SCRIPT_DIR/index.js" "$@"