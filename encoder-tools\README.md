# Encoder Tools

Alat untuk melakukan encoding pada JSON, HTML, dan JavaScript secara otomatis dan menyimpan hasilnya di folder output.

## Deskripsi

Encoder Tools adalah utilitas command-line sederhana yang memungkinkan Anda untuk melakukan encoding pada:

- File JSON
- File HTML
- File JavaScript
- String JSON, HTML, dan JavaScript
- Semua file dalam folder (rekursif)

Hasil encoding akan disimpan di folder `output` dan juga ditampilkan di konsol.

## Instalasi

### Instalasi Lokal

```bash
# Pindah ke direktori encoder-tools
cd encoder-tools

# Instal dependensi (jika ada)
npm install
```

### Instalasi Global (Opsional)

```bash
# Instal secara global
npm install -g ./
```

## Penggunaan

### Menggunakan Node.js

```bash
node index.js <perintah> [opsi]
```

### Menggunakan CLI (jika diinstal secara global)

```bash
encoder-tools <perintah> [opsi]
```

## Perintah

### Encode File Tunggal

```bash
node index.js encode-file <path-file> [output-filename]
```

Contoh:
```bash
node index.js encode-file ./data.json encoded_data.txt
```

### Encode Semua File dalam Folder

```bash
node index.js encode-folder <path-folder>
```

Contoh:
```bash
node index.js encode-folder ./my-files
```

### Encode String JSON

```bash
node index.js encode-json <string-json> [output-filename]
```

Contoh:
```bash
node index.js encode-json '{"name":"John","age":30}' my_json.txt
```

### Encode String HTML

```bash
node index.js encode-html <string-html> [output-filename]
```

Contoh:
```bash
node index.js encode-html '<div>Hello World</div>' my_html.txt
```

### Encode String JavaScript

```bash
node index.js encode-js <string-js> [output-filename]
```

Contoh:
```bash
node index.js encode-js 'function test() { return true; }' my_js.txt
```

### Bantuan

```bash
node index.js help
```

## Struktur Folder

```
encoder-tools/
├── index.js         # File utama CLI
├── encoder.js       # Modul encoder
├── package.json     # Konfigurasi package
├── README.md        # Dokumentasi
└── output/          # Folder untuk hasil encoding
```

## Metode Encoding

- **JSON**: Menggunakan `encodeURIComponent()` untuk encoding JSON string
- **HTML**: Mengubah karakter khusus (`&`, `<`, `>`, `"`, `'`) menjadi entitas HTML
- **JavaScript**: Menggunakan `encodeURIComponent()` untuk encoding JavaScript

## Lisensi

MIT