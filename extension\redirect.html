<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        .redirect-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .redirect-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .redirect-content {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.9);
        }

        .countdown-container {
            margin-bottom: 30px;
        }

        .countdown-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        .countdown-number {
            font-size: 48px;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 3px;
            transition: width 0.1s linear;
            box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
        }

        .redirect-url {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            word-break: break-all;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="loading-spinner"></div>
        <h1 class="redirect-title" id="redirectTitle">Menyiapkan Pengalihan...</h1>
        <p class="redirect-content" id="redirectContent">Mohon tunggu sebentar...</p>
        
        <div class="countdown-container">
            <div class="countdown-text">Mengalihkan dalam</div>
            <div class="countdown-number" id="countdownNumber">3</div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
        </div>
        
        <div class="redirect-url" id="redirectUrl">Menyiapkan URL...</div>
    </div>

    <script>
        let redirectData = null;
        let countdownInterval = null;
        let progressInterval = null;

        // Listen for messages from the extension
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'setupCustomRedirect') {
                redirectData = message.data;
                setupRedirect();
            }
        });

        function setupRedirect() {
            if (!redirectData) return;

            // Update UI elements
            document.getElementById('redirectTitle').textContent = redirectData.title;
            document.getElementById('redirectContent').textContent = redirectData.content;
            document.getElementById('redirectUrl').textContent = redirectData.redirectUrl;
            document.title = redirectData.title;

            const delay = redirectData.delay || 3000;
            const countdownSeconds = Math.ceil(delay / 1000);
            let currentSecond = countdownSeconds;

            const countdownElement = document.getElementById('countdownNumber');
            const progressElement = document.getElementById('progressFill');

            // Update countdown
            countdownElement.textContent = currentSecond;

            // Start countdown
            countdownInterval = setInterval(() => {
                currentSecond--;
                countdownElement.textContent = currentSecond;

                if (currentSecond <= 0) {
                    clearInterval(countdownInterval);
                    performRedirect();
                }
            }, 1000);

            // Start progress bar
            let progress = 0;
            const progressStep = 100 / (delay / 100);
            
            progressInterval = setInterval(() => {
                progress += progressStep;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                }
                progressElement.style.width = progress + '%';
            }, 100);
        }

        function performRedirect() {
            if (redirectData && redirectData.redirectUrl) {
                window.location.href = redirectData.redirectUrl;
            }
        }

        // Cleanup intervals when page is unloaded
        window.addEventListener('beforeunload', () => {
            if (countdownInterval) clearInterval(countdownInterval);
            if (progressInterval) clearInterval(progressInterval);
        });
    </script>
</body>
</html>