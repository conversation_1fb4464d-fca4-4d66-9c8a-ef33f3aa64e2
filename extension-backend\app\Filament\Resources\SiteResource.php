<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteResource\Pages;
use App\Filament\Resources\SiteResource\RelationManagers;
use App\Models\Site;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SiteResource extends Resource
{
    protected static ?string $model = Site::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?string $navigationLabel = 'Situs Web';

    protected static ?string $modelLabel = 'Situs Web';

    protected static ?string $pluralModelLabel = 'Situs Web';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama Situs')
                    ->required()
                    ->maxLength(255),
                
                Forms\Components\TextInput::make('url')
                    ->label('URL')
                    ->url()
                    ->required()
                    ->maxLength(255),
                
                Forms\Components\TextInput::make('domain')
                    ->label('Domain')
                    ->required()
                    ->maxLength(255),
                
                Forms\Components\Select::make('category')
                    ->label('Kategori')
                    ->options(function () {
                        return \App\Models\Category::active()->ordered()->pluck('name', 'slug')->toArray();
                    })
                    ->searchable()
                    ->required()
                    ->default('general')
                    ->helperText('Pilih kategori fungsional untuk situs ini'),
                
                Forms\Components\Select::make('visibility')
                    ->label('Visibilitas')
                    ->options([
                        'elite' => 'Hanya Elite',
                        'premium' => 'Hanya Premium',
                        'both' => 'Elite & Premium',
                    ])
                    ->required()
                    ->default('both')
                    ->helperText('Tentukan siapa yang dapat melihat situs ini'),
                
                Forms\Components\FileUpload::make('thumbnail')
                    ->label('Thumbnail')
                    ->image()
                    ->directory('thumbnails')
                    ->visibility('public'),
                
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->rows(3),
                
                Forms\Components\FileUpload::make('cookie_file_path')
                    ->label('Upload File Cookies (JSON)')
                    ->acceptedFileTypes(['application/json', '.json'])
                    ->directory('cookie-files')
                    ->visibility('private')
                    ->helperText('Upload file JSON yang berisi data cookies')
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state) {
                            $set('cookie_file_uploaded_at', now());
                        }
                    }),
                
                Forms\Components\Hidden::make('cookie_file_uploaded_at'),
                
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->label('Thumbnail')
                    ->circular()
                    ->size(40),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Situs')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('domain')
                    ->label('Domain')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('category')
                    ->label('Kategori')
                    ->badge()
                    ->color('primary')
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),
                
                Tables\Columns\TextColumn::make('visibility')
                    ->label('Visibilitas')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'elite' => 'warning',
                        'premium' => 'info',
                        'both' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'elite' => 'Hanya Elite',
                        'premium' => 'Hanya Premium',
                        'both' => 'Elite & Premium',
                        default => $state,
                    }),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\IconColumn::make('has_cookie_file')
                    ->label('Cookie File')
                    ->getStateUsing(fn ($record) => !empty($record->cookie_file_path))
                    ->boolean()
                    ->trueIcon('heroicon-o-document')
                    ->falseIcon('heroicon-o-document-text')
                    ->trueColor('success')
                    ->falseColor('gray')
                    ->tooltip(fn ($record) => $record->cookie_file_path ? 'File uploaded' : 'Manual entry'),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('Kategori')
                    ->options(function () {
                        return \App\Models\Category::active()->ordered()->pluck('name', 'slug')->toArray();
                    })
                    ->searchable(),
                
                Tables\Filters\SelectFilter::make('visibility')
                    ->label('Visibilitas')
                    ->options([
                        'elite' => 'Hanya Elite',
                        'premium' => 'Hanya Premium',
                        'both' => 'Elite & Premium',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSites::route('/'),
            'create' => Pages\CreateSite::route('/create'),
            'edit' => Pages\EditSite::route('/{record}/edit'),
        ];
    }
}
