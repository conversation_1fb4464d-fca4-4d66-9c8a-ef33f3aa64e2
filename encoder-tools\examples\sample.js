/**
 * sample.js - Contoh file JavaScript untuk Encoder Tools
 * 
 * File ini berisi berbagai contoh kode JavaScript yang dapat digunakan
 * untuk menguji fungsionalitas encoding dari Encoder Tools.
 */

// Deklarasi variabel dengan berbagai tipe data
const appName = 'Encoder Tools';
const version = '1.0.0';
const isActive = true;
const features = ['JSON', 'HTML', 'JavaScript', 'File', 'Folder'];
const specialChars = '!@#$%^&*()_+{}[]|:;"\'\\\'<>,.?/~`';

// Objek konfigurasi
const config = {
  outputFolder: './output',
  supportedExtensions: ['.json', '.html', '.htm', '.js'],
  encoding: 'UTF-8',
  options: {
    prettyPrint: false,
    saveToFile: true,
    logToConsole: true
  }
};

/**
 * Fungsi untuk menampilkan informasi tentang aplikasi
 * @returns {string} Informasi aplikasi
 */
function getAppInfo() {
  return `${appName} v${version}`;
}

/**
 * Fungsi untuk memeriksa apakah ekstensi file didukung
 * @param {string} filename - Nama file yang akan diperiksa
 * @returns {boolean} True jika ekstensi didukung, false jika tidak
 */
function isSupportedExtension(filename) {
  const ext = filename.substring(filename.lastIndexOf('.'));
  return config.supportedExtensions.includes(ext);
}

/**
 * Kelas untuk mengelola proses encoding
 */
class Encoder {
  constructor(type) {
    this.type = type;
    this.initialized = false;
  }

  initialize() {
    console.log(`Initializing ${this.type} encoder...`);
    this.initialized = true;
  }

  encode(input) {
    if (!this.initialized) {
      this.initialize();
    }

    console.log(`Encoding ${this.type}...`);
    
    // Contoh implementasi sederhana
    switch(this.type) {
      case 'json':
        return encodeURIComponent(JSON.stringify(input));
      case 'html':
        return input
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;');
      case 'javascript':
        return encodeURIComponent(input);
      default:
        throw new Error(`Unsupported encoding type: ${this.type}`);
    }
  }
}

// Contoh penggunaan arrow function
const logResult = (result) => {
  console.log('Hasil encoding:');
  console.log(result);
};

// Contoh penggunaan Promise
function encodeAsync(input, type) {
  return new Promise((resolve, reject) => {
    try {
      const encoder = new Encoder(type);
      const result = encoder.encode(input);
      setTimeout(() => {
        resolve(result);
      }, 100);
    } catch (error) {
      reject(error);
    }
  });
}

// Contoh penggunaan async/await
async function processEncoding() {
  try {
    const jsonResult = await encodeAsync({ name: 'Test' }, 'json');
    const htmlResult = await encodeAsync('<div>Hello</div>', 'html');
    const jsResult = await encodeAsync('function test() { return true; }', 'javascript');
    
    return {
      json: jsonResult,
      html: htmlResult,
      javascript: jsResult
    };
  } catch (error) {
    console.error('Error during encoding:', error);
    return null;
  }
}

// Contoh penggunaan template literals
const generateReport = (results) => `
Encoding Report:
----------------
JSON: ${results.json}
HTML: ${results.html}
JavaScript: ${results.javascript}
----------------
Generated at: ${new Date().toISOString()}
`;

// Export modul
module.exports = {
  getAppInfo,
  isSupportedExtension,
  Encoder,
  encodeAsync,
  processEncoding,
  generateReport,
  config
};

// Contoh kode yang akan dijalankan jika file ini dieksekusi langsung
if (require.main === module) {
  console.log(getAppInfo());
  console.log('Supported extensions:', config.supportedExtensions);
  
  const encoder = new Encoder('json');
  const result = encoder.encode({ test: 'Hello World' });
  logResult(result);
  
  processEncoding().then(results => {
    if (results) {
      console.log(generateReport(results));
    }
  });
}