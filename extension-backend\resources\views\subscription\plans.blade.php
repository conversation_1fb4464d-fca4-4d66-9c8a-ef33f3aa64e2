<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .plan-card {
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            transform: translateY(-5px);
        }
        .plan-card.selected {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }
        .step-indicator.completed {
            background: #10b981;
            color: white;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <!-- Navigation -->
    <nav class="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <a href="{{ url('/') }}" class="flex items-center space-x-3">
                        <!-- Logo Satu Pintu -->
                        <svg width="40" height="40" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle cx="32" cy="32" r="30" fill="url(#doorGradient)"/>
                            <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="#ffffff" opacity="0.9"/>
                            <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradient)"/>
                            <text x="32" y="38" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#fbbf24">1</text>
                        </svg>
                        <span class="text-xl font-semibold">Satu Pintu</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url('/') }}" class="text-gray-300 hover:text-white transition-colors">
                        <i class="fas fa-home mr-2"></i>Beranda
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Step Indicator -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4">
                <div class="step-indicator active flex items-center justify-center w-10 h-10 rounded-full border-2 border-white/20">
                    <span class="text-sm font-semibold">1</span>
                </div>
                <div class="w-16 h-0.5 bg-white/20"></div>
                <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full border-2 border-white/20">
                    <span class="text-sm font-semibold">2</span>
                </div>
                <div class="w-16 h-0.5 bg-white/20"></div>
                <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full border-2 border-white/20">
                    <span class="text-sm font-semibold">3</span>
                </div>
            </div>
        </div>

        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">Pilih Paket Berlangganan</h1>
            <p class="text-xl text-gray-300">Mulai perjalanan Anda dengan paket yang sesuai kebutuhan</p>
        </div>

        <!-- Multi-Step Form -->
        <div class="max-w-6xl mx-auto">
            <!-- Step 1: Plan Selection -->
            <div id="step1" class="step-content">
                <div class="grid md:grid-cols-3 gap-8 mb-8">
                    @foreach($plans as $planKey => $plan)
                    <div class="plan-card bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 cursor-pointer"
                         data-plan="{{ $planKey }}" data-price="{{ $plan['price'] }}">
                        @if($plan['popular'])
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold px-3 py-1 rounded-full inline-block mb-4">
                            <i class="fas fa-star mr-1"></i>TERPOPULER
                        </div>
                        @endif

                        <h3 class="text-2xl font-bold mb-2">{{ $plan['name'] }}</h3>
                        <div class="text-3xl font-bold mb-4">
                            <span class="text-yellow-400">Rp {{ number_format($plan['price'], 0, ',', '.') }}</span>
                            <span class="text-sm text-gray-400">/bulan</span>
                        </div>

                        <ul class="space-y-3 mb-6">
                            @foreach($plan['features'] as $feature)
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-400 mr-3"></i>
                                <span class="text-gray-300">{{ $feature }}</span>
                            </li>
                            @endforeach
                        </ul>

                        <div class="text-center">
                            <button class="select-plan-btn w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                                Pilih Paket
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Step 2: Registration Form -->
            <div id="step2" class="step-content hidden">
                <div class="max-w-2xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                    <h2 class="text-2xl font-bold mb-6 text-center">Informasi Akun</h2>

                    <form id="registrationForm" class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium mb-2">Nama Lengkap *</label>
                                <input type="text" id="name" name="name" required
                                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                       placeholder="Masukkan nama lengkap">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium mb-2">Email *</label>
                                <input type="email" id="email" name="email" required
                                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="password" class="block text-sm font-medium mb-2">Password *</label>
                                <input type="password" id="password" name="password" required
                                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                       placeholder="Minimal 8 karakter">
                            </div>
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium mb-2">Konfirmasi Password *</label>
                                <input type="password" id="password_confirmation" name="password_confirmation" required
                                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                       placeholder="Ulangi password">
                            </div>
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium mb-2">Nomor Telepon (Opsional)</label>
                            <input type="tel" id="phone" name="phone"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                   placeholder="+62812345678">
                        </div>
                    </form>

                    <div class="flex justify-between mt-8">
                        <button id="backToPlans" class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Kembali
                        </button>
                        <button id="continueToPayment" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-lg transition-all">
                            Lanjutkan <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Order Summary & Discount -->
            <div id="step3" class="step-content hidden">
                <div class="max-w-2xl mx-auto">
                    <!-- Order Summary & Discount -->
                    <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                        <h3 class="text-xl font-bold mb-6">Ringkasan Pesanan</h3>

                            <!-- Selected Plan Info -->
                            <div id="selectedPlanInfo" class="mb-6 p-4 bg-white/5 rounded-lg">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-semibold" id="planName">-</span>
                                    <span class="text-yellow-400 font-bold" id="planPrice">Rp 0</span>
                                </div>
                                <div class="text-sm text-gray-400" id="planDuration">30 hari</div>
                            </div>

                            <!-- Discount Code -->
                            <div class="mb-6">
                                <label for="discount_code" class="block text-sm font-medium mb-2">Kode Diskon (Opsional)</label>
                                <div class="flex space-x-2">
                                    <input type="text" id="discount_code" name="discount_code"
                                           class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                           placeholder="Masukkan kode diskon">
                                    <button id="applyDiscount" class="px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                                <div id="discountMessage" class="mt-2 text-sm"></div>
                            </div>

                            <!-- Price Breakdown -->
                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span id="subtotal">Rp 0</span>
                                </div>
                                <div id="discountRow" class="flex justify-between text-green-400 hidden">
                                    <span>Diskon:</span>
                                    <span id="discountAmount">-Rp 0</span>
                                </div>
                                <hr class="border-white/20">
                                <div class="flex justify-between text-xl font-bold">
                                    <span>Total:</span>
                                    <span class="text-yellow-400" id="totalAmount">Rp 0</span>
                                </div>
                            </div>

                            <!-- Available Discount Codes -->
                            <div class="mb-6">
                                <h4 class="text-sm font-medium mb-3">Kode Diskon Tersedia:</h4>
                                <div class="space-y-2 text-xs">
                                    <div class="flex justify-between p-2 bg-white/5 rounded">
                                        <span class="font-mono">WELCOME10</span>
                                        <span class="text-green-400">10% off</span>
                                    </div>
                                    <div class="flex justify-between p-2 bg-white/5 rounded">
                                        <span class="font-mono">PREMIUM20</span>
                                        <span class="text-green-400">20% off</span>
                                    </div>
                                </div>
                        </div>
                    </div>

                    <div class="flex justify-between mt-8">
                        <button id="backToRegistration" class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>Kembali
                        </button>
                        <button id="proceedToPayment" class="px-8 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold rounded-lg transition-all text-lg">
                            <i class="fas fa-credit-card mr-2"></i>Bayar Sekarang
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p class="text-lg font-semibold">Memproses pembayaran...</p>
            <p class="text-sm text-gray-400 mt-2">Mohon tunggu sebentar</p>
        </div>
    </div>

    <script>
        // State management
        let currentStep = 1;
        let selectedPlan = null;
        let selectedPrice = 0;
        let discountApplied = false;
        let discountAmount = 0;
        let finalPrice = 0;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStepIndicator();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Plan selection
            document.querySelectorAll('.plan-card').forEach(card => {
                card.addEventListener('click', function() {
                    selectPlan(this);
                });
            });

            // Navigation buttons
            document.getElementById('backToPlans').addEventListener('click', () => goToStep(1));
            document.getElementById('continueToPayment').addEventListener('click', validateAndContinue);
            document.getElementById('backToRegistration').addEventListener('click', () => goToStep(2));
            document.getElementById('proceedToPayment').addEventListener('click', processPayment);



            // Discount code
            document.getElementById('applyDiscount').addEventListener('click', applyDiscountCode);
            document.getElementById('discount_code').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    applyDiscountCode();
                }
            });
        }

        function selectPlan(planCard) {
            // Remove previous selection
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select current plan
            planCard.classList.add('selected');
            selectedPlan = planCard.dataset.plan;
            selectedPrice = parseInt(planCard.dataset.price);

            // Update UI
            updatePlanInfo();

            // Auto proceed to next step
            setTimeout(() => goToStep(2), 500);
        }



        function updatePlanInfo() {
            if (!selectedPlan) return;

            const plans = @json($plans);
            const plan = plans[selectedPlan];

            document.getElementById('planName').textContent = plan.name;
            document.getElementById('planPrice').textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(plan.price);
            document.getElementById('subtotal').textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(plan.price);

            updateTotalPrice();
        }

        function updateTotalPrice() {
            finalPrice = selectedPrice - discountAmount;
            document.getElementById('totalAmount').textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(finalPrice);
        }

        function goToStep(step) {
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Show target step
            document.getElementById('step' + step).classList.remove('hidden');
            currentStep = step;
            updateStepIndicator();
        }

        function updateStepIndicator() {
            document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
                indicator.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    indicator.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    indicator.classList.add('active');
                }
            });
        }

        function validateAndContinue() {
            const form = document.getElementById('registrationForm');
            const formData = new FormData(form);

            // Basic validation
            const name = formData.get('name');
            const email = formData.get('email');
            const password = formData.get('password');
            const passwordConfirmation = formData.get('password_confirmation');

            if (!name || !email || !password || !passwordConfirmation) {
                alert('Mohon lengkapi semua field yang wajib diisi');
                return;
            }

            if (password !== passwordConfirmation) {
                alert('Password dan konfirmasi password tidak sama');
                return;
            }

            if (password.length < 8) {
                alert('Password minimal 8 karakter');
                return;
            }

            goToStep(3);
        }

        async function applyDiscountCode() {
            const discountCode = document.getElementById('discount_code').value.trim();
            if (!discountCode) return;

            try {
                const response = await fetch('/api/validate-discount', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        discount_code: discountCode,
                        amount: selectedPrice
                    })
                });

                const result = await response.json();
                const messageEl = document.getElementById('discountMessage');

                if (result.status === 'success') {
                    discountApplied = true;
                    discountAmount = result.data.discount_amount;

                    messageEl.textContent = 'Kode diskon berhasil diterapkan!';
                    messageEl.className = 'mt-2 text-sm text-green-400';

                    document.getElementById('discountRow').classList.remove('hidden');
                    document.getElementById('discountAmount').textContent = '-Rp ' + new Intl.NumberFormat('id-ID').format(discountAmount);

                    updateTotalPrice();
                } else {
                    messageEl.textContent = result.message;
                    messageEl.className = 'mt-2 text-sm text-red-400';

                    discountApplied = false;
                    discountAmount = 0;
                    document.getElementById('discountRow').classList.add('hidden');
                    updateTotalPrice();
                }
            } catch (error) {
                console.error('Error applying discount:', error);
                document.getElementById('discountMessage').textContent = 'Terjadi kesalahan saat memvalidasi kode diskon';
                document.getElementById('discountMessage').className = 'mt-2 text-sm text-red-400';
            }
        }

        async function processPayment() {
            const form = document.getElementById('registrationForm');
            const formData = new FormData(form);

            const registrationData = {
                name: formData.get('name'),
                email: formData.get('email'),
                password: formData.get('password'),
                password_confirmation: formData.get('password_confirmation'),
                phone: formData.get('phone'),
                plan: selectedPlan,
                discount_code: discountApplied ? document.getElementById('discount_code').value : null
            };

            document.getElementById('loadingModal').classList.remove('hidden');

            try {
                const response = await fetch('/api/subscription/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(registrationData)
                });

                const result = await response.json();

                if (result.status === 'success') {
                    // Store user data for payment success callback
                    localStorage.setItem('pendingUser', JSON.stringify({
                        order_id: result.data.order_id,
                        name: formData.get('name'),
                        email: formData.get('email'),
                        plan: selectedPlan
                    }));

                    // Open Midtrans Snap payment
                    if (result.data.snap_token) {
                        // Load Midtrans Snap script if not already loaded
                        if (!window.snap) {
                            const script = document.createElement('script');
                            // Gunakan URL yang sesuai dengan environment (production/sandbox)
                            const isProduction = {{ config('midtrans.is_production', false) ? 'true' : 'false' }};
                            script.src = isProduction ? 'https://app.midtrans.com/snap/snap.js' : 'https://app.sandbox.midtrans.com/snap/snap.js';
                            script.setAttribute('data-client-key', '{{ config("midtrans.client_key") }}');
                            document.head.appendChild(script);
                            
                            script.onload = function() {
                                openSnapPayment(result.data.snap_token);
                            };
                        } else {
                            openSnapPayment(result.data.snap_token);
                        }
                    } else {
                        throw new Error('Snap token tidak ditemukan');
                    }
                } else {
                    throw new Error(result.message || 'Terjadi kesalahan saat memproses registrasi');
                }
            } catch (error) {
                console.error('Error processing payment:', error);
                alert('Terjadi kesalahan: ' + error.message);
            } finally {
                document.getElementById('loadingModal').classList.add('hidden');
            }
        }

        // Function to open Midtrans Snap payment
        function openSnapPayment(snapToken) {
            window.snap.pay(snapToken, {
                onSuccess: function(result) {
                    console.log('Payment success:', result);
                    alert('Pembayaran berhasil! Akun Anda telah dibuat.');
                    
                    // Redirect to success page or dashboard
                    window.location.href = '{{ url("/subscription/payment-success") }}?order_id=' + result.order_id;
                },
                onPending: function(result) {
                    console.log('Payment pending:', result);
                    alert('Pembayaran sedang diproses. Kami akan memberitahu Anda setelah pembayaran dikonfirmasi.');
                    
                    // Redirect to pending page
                    window.location.href = '{{ url("/") }}';
                },
                onError: function(result) {
                    console.log('Payment error:', result);
                    alert('Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.');
                },
                onClose: function() {
                    console.log('Payment popup closed');
                    alert('Pembayaran dibatalkan.');
                }
            });
        }
    </script>
</body>
</html>
