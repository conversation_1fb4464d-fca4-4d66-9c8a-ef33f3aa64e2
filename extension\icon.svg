<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="numberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="4" dy="4" stdDeviation="6" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#doorGradient)" filter="url(#shadow)"/>
  
  <!-- Door Frame -->
  <rect x="36" y="24" width="56" height="80" rx="4" ry="4" fill="#ffffff" opacity="0.9"/>
  <rect x="40" y="28" width="48" height="72" rx="2" ry="2" fill="url(#doorGradient)"/>
  
  <!-- Door Handle -->
  <circle cx="76" cy="64" r="4" fill="#fbbf24"/>
  <circle cx="76" cy="64" r="3" fill="#f59e0b"/>
  
  <!-- Door Panel Lines -->
  <rect x="44" y="36" width="40" height="2" fill="rgba(255,255,255,0.3)"/>
  <rect x="44" y="44" width="40" height="2" fill="rgba(255,255,255,0.3)"/>
  <rect x="44" y="80" width="40" height="2" fill="rgba(255,255,255,0.3)"/>
  <rect x="44" y="88" width="40" height="2" fill="rgba(255,255,255,0.3)"/>
  
  <!-- Number "1" -->
  <g transform="translate(52, 52)">
    <path d="M16 4 L16 36 M10 10 L16 4 L16 36 M8 36 L24 36" 
          stroke="url(#numberGradient)" 
          stroke-width="6" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"/>
    <path d="M16 4 L16 36 M10 10 L16 4 L16 36 M8 36 L24 36" 
          stroke="#ffffff" 
          stroke-width="3" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="30" cy="30" r="3" fill="rgba(255,255,255,0.4)"/>
  <circle cx="98" cy="30" r="2" fill="rgba(255,255,255,0.3)"/>
  <circle cx="30" cy="98" r="2" fill="rgba(255,255,255,0.3)"/>
  <circle cx="98" cy="98" r="3" fill="rgba(255,255,255,0.4)"/>
</svg>