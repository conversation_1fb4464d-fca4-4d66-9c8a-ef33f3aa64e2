// Configuration
const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Global variables
let currentUser = null;
let currentToken = null;
let availableSites = [];
let isInitializing = false;
let isInitialized = false;
let isProcessingLogin = false;
let initTimeout = null;

// DOM elements
let loginForm, loginBtn, emailInput, passwordInput, logoutBtn, loginScreen, mainScreen, userInfo, sitesList, statusDiv, userName, userRole, loginStatus, troubleshootingDiv, fullPageLoader, subscriptionInfo, filterTabs, searchInput, appVersion;

// Categories data
let availableCategories = [];

// Initialize when DOM is loaded with debouncing
function debouncedInit() {
    if (initTimeout) {
        clearTimeout(initTimeout);
    }
    initTimeout = setTimeout(init, 100); // 100ms debounce
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    loginForm = document.getElementById('loginForm');
    loginBtn = document.getElementById('loginBtn');
    emailInput = document.getElementById('email');
    passwordInput = document.getElementById('password');
    logoutBtn = document.getElementById('logoutBtn');
    loginScreen = document.getElementById('loginScreen');
    mainScreen = document.getElementById('mainScreen');
    userInfo = document.getElementById('userInfo');
    sitesList = document.getElementById('sitesList');
    statusDiv = document.getElementById('status');
    userName = document.getElementById('userName');
    userRole = document.getElementById('userRole');
    loginStatus = document.getElementById('loginStatus');
    troubleshootingDiv = document.getElementById('troubleshooting');
    fullPageLoader = document.getElementById('fullPageLoader');
    subscriptionInfo = document.getElementById('subscriptionInfo');
    filterTabs = document.querySelectorAll('.filter-tab');
    searchInput = document.getElementById('searchInput');
    appVersion = document.getElementById('appVersion');
    
    // Add event listeners
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Add search input event listener
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value;
            const activeTab = document.querySelector('.filter-tab.active');
            const category = activeTab ? activeTab.dataset.category : 'all';
            renderSites(category, searchTerm);
        });
    }
    
    // Add filter tab event listeners (will be updated when categories are loaded)
    if (document.getElementById('filterTabs')) {
        setupFilterTabListeners();
    }
    
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'loginSuccessFromBackground') {
            console.log('Popup received login success from background:', request.authData);
            console.log('Current processing state:', { isProcessingLogin, isInitialized });
            handleLoginSuccess(request.authData.token, request.authData.user);
            sendResponse({ success: true });
        }
    });
    
    // Initialize with debouncing
    debouncedInit();
});

async function init() {
    // Clear any pending init timeout
    if (initTimeout) {
        clearTimeout(initTimeout);
        initTimeout = null;
    }
    
    // Prevent multiple initialization
    if (isInitializing || isInitialized) {
        console.log('Init already in progress or completed, skipping...');
        return;
    }
    
    // Set popup size
    try {
        document.body.style.width = '420px';
        document.body.style.height = '600px';
        document.body.style.minWidth = '420px';
        document.body.style.minHeight = '600px';
        document.body.style.maxWidth = '420px';
        document.body.style.maxHeight = '600px';
    } catch (error) {
        console.log('Could not set popup size:', error);
    }
    
    isInitializing = true;
    console.log('Initializing popup...', {
        timestamp: new Date().toISOString(),
        flags: { isInitializing, isInitialized, isProcessingLogin }
    });
    
    try {
        showFullPageLoading(true, 'Memulai aplikasi...', 'Memeriksa status login');
        
        // Clear any existing badge when popup opens
        try {
            await chrome.action.setBadgeText({ text: '' });
        } catch (error) {
            console.log('Could not clear badge:', error);
        }
        
        // First try to get cached data
        showFullPageLoading(true, 'Memeriksa data tersimpan...', 'Mencari sesi yang aktif');
        const cachedData = await getCachedData();
        
        if (cachedData && cachedData.userSession) {
            console.log('Using cached session data');
            showFullPageLoading(true, 'Memuat data dari cache...', 'Menyiapkan antarmuka');
            
            currentUser = cachedData.userSession.user;
            currentToken = cachedData.userSession.token;
            availableSites = cachedData.sitesData?.data || [];
            
            // Add small delay to show loading message
            await new Promise(resolve => setTimeout(resolve, 500));
            
            showMainScreen();
            updateUI();
            renderSites();
        } else {
            // Check stored auth data first before showing login screen
            showFullPageLoading(true, 'Memeriksa autentikasi...', 'Memvalidasi token akses');
            const storedAuth = await getStoredAuth();
            
            if (storedAuth && storedAuth.token) {
                console.log('Found stored auth, checking status');
                await checkAuthStatus();
            } else {
                console.log('No stored auth found, showing login screen');
                showLoginScreen();
            }
        }
    } catch (error) {
        console.error('Initialization error:', error);
        showFullPageLoading(true, 'Terjadi kesalahan...', 'Menampilkan halaman login');
        await new Promise(resolve => setTimeout(resolve, 1000));
        showLoginScreen();
    } finally {
        showFullPageLoading(false);
        isInitializing = false;
        isInitialized = true;
        console.log('Popup initialization completed successfully', {
            timestamp: new Date().toISOString(),
            flags: { isInitializing, isInitialized, isProcessingLogin }
        });
    }
}

// Check authentication status on popup load
async function checkAuthStatus() {
    try {
        const authData = await getStoredAuth();
        if (!authData || !authData.token) {
            showLoginScreen();
            return;
        }

        // Set current data from stored auth immediately
        currentToken = authData.token;
        currentUser = authData.user;
        
        // Show main screen immediately to avoid login screen flash
        showMainScreen();
        
        // Verify token with server in background
        try {
            showFullPageLoading(true, 'Memverifikasi akses...', 'Memeriksa token autentikasi');
            
            const response = await fetch(`${API_BASE_URL}/auth/me`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authData.token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Update user data if needed
                    currentUser = data.user;
                    showFullPageLoading(true, 'Memuat daftar situs...', 'Mengambil data dari server');
                    await loadSites();
                    updateUI();
                    return;
                }
            } else if (response.status === 403) {
                // Handle subscription expired
                const data = await response.json();
                if (data.subscription_expired) {
                    showFullPageLoading(true, 'Akun tidak aktif...', 'Masa aktif akun telah berakhir');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    await clearStoredAuth();
                    currentToken = null;
                    currentUser = null;
                    showFullPageLoading(false);
                    showLoginScreen();
                    showLoginStatus('Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.', 'error');
                    return;
                }
            }
            
            // Token is invalid, remove it and show login screen
            showFullPageLoading(true, 'Token tidak valid...', 'Mengarahkan ke halaman login');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await clearStoredAuth();
            currentToken = null;
            currentUser = null;
            showFullPageLoading(false);
            showLoginScreen();
            
        } catch (verifyError) {
            console.error('Error verifying token:', verifyError);
            // Keep showing main screen with cached data if verification fails
            // This handles offline scenarios
            showFullPageLoading(true, 'Mode offline...', 'Menggunakan data tersimpan');
            await new Promise(resolve => setTimeout(resolve, 500));
            await loadSites();
            updateUI();
        }
        
    } catch (error) {
        console.error('Error checking auth status:', error);
        showFullPageLoading(true, 'Terjadi kesalahan...', 'Menampilkan halaman login');
        await new Promise(resolve => setTimeout(resolve, 1000));
        showFullPageLoading(false);
        showLoginScreen();
    }
}

async function handleLogin(event) {
    event.preventDefault();
    
    const email = emailInput.value.trim();
    const password = passwordInput.value.trim();
    
    if (!email || !password) {
        showLoginStatus('Mohon isi email dan password', 'error');
        return;
    }
    
    // Show loading state
    setLoginLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
            // Save authentication data
            currentUser = data.user;
            currentToken = data.token;
            
            // Store in chrome storage
            try {
                await saveAuth({ token: currentToken, user: currentUser });
            } catch (storageError) {
                console.warn('Failed to save auth data:', storageError);
            }
            
            // Clear form
            emailInput.value = '';
            passwordInput.value = '';
            
            // Hide troubleshooting section if visible
            hideTroubleshooting();
            
            // Show success message
            showLoginStatus('Login berhasil! Selamat datang, ' + data.user.name, 'success');
            
            // Notify background script
            try {
                chrome.runtime.sendMessage({
                    action: 'loginSuccess',
                    authData: {
                        token: currentToken,
                        user: currentUser
                    },
                    source: 'popup_init'
                });
            } catch (e) {
                console.log('Could not send message to background:', e);
            }
            
            // Load sites and show main screen
            showFullPageLoading(true, 'Login berhasil!', 'Menyiapkan dashboard Anda...');
            
            // Add small delay to show success message
            await new Promise(resolve => setTimeout(resolve, 800));
            
            showMainScreen();
            await loadSites();
            
            // Hide loading after everything is ready
            showFullPageLoading(false);
            
        } else {
            showLoginStatus(data.message || 'Login gagal. Periksa email dan password Anda.', 'error');
        }
        
    } catch (error) {
        console.error('Login error:', error);
        if (error.message && error.message.includes('Extension context invalidated')) {
            showLoginStatus('Ekstensi perlu di-reload untuk login.', 'error');
            showTroubleshooting();
        } else {
            showLoginStatus('Terjadi kesalahan saat login. Silakan coba lagi.', 'error');
        }
    } finally {
        setLoginLoading(false);
    }
}

function setLoginLoading(isLoading) {
    if (loginBtn) {
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');
        
        if (isLoading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-flex';
            loginBtn.disabled = true;
            emailInput.disabled = true;
            passwordInput.disabled = true;
        } else {
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            loginBtn.disabled = false;
            emailInput.disabled = false;
            passwordInput.disabled = false;
        }
    }
}

// Handle login success from extension success page
async function handleLoginSuccess(token, user) {
    // Prevent multiple processing
    if (isProcessingLogin) {
        console.log('Login already being processed, ignoring duplicate call');
        return;
    }
    
    isProcessingLogin = true;
    
    try {
        console.log('handleLoginSuccess called with:', { token: token ? 'present' : 'missing', user: user ? 'present' : 'missing' });
        
        // Validate parameters
        if (!token) {
            throw new Error('Token parameter is undefined or empty');
        }
        
        if (!user) {
            throw new Error('User parameter is undefined or empty');
        }
        
        // Save authentication data with subscription info
        currentUser = {
            ...user,
            subscription_expires_at: user.subscription_expires_at,
            subscription_active: user.subscription_active,
            days_until_expiry: user.days_until_expiry
        };
        currentToken = token;
        
        // Store in chrome storage using our wrapper function
        try {
            await saveAuth({ token: currentToken, user: currentUser });
        } catch (storageError) {
            console.warn('Failed to save auth data:', storageError);
            // Continue anyway as we have the data in memory
        }
        
        // Note: loginSuccess message is now sent by content script, not popup
        // This prevents duplicate processing and looping issues
        console.log('Login success handled by popup, waiting for background caching...');
        
        // Hide troubleshooting section if visible
        hideTroubleshooting();
        
        // Show success message
        showStatus('Login berhasil! Data disinkronkan. Selamat datang, ' + user.name, 'success');
        
        // Load sites and show main screen
        showMainScreen();
        
        // Wait a shorter time for background script to finish caching
        setTimeout(async () => {
            try {
                // Try to get cached data first, fallback to API if needed
                const cachedData = await getCachedData();
                if (cachedData && cachedData.sitesData) {
                    availableSites = cachedData.sitesData.data || [];
                    renderSites();
                    updateUI();
                    console.log('Sites loaded from cache successfully');
                } else {
                    console.log('No cached sites found, loading from API');
                    await loadSites();
                }
            } catch (error) {
                console.error('Error loading sites after login:', error);
                await loadSites();
            }
        }, 500);
        
    } catch (error) {
        console.error('Error handling login success:', error);
        if (error.message && error.message.includes('Extension context invalidated')) {
            showStatus('Ekstensi perlu di-reload untuk login.', 'error');
            showTroubleshooting();
        } else {
            showStatus('Error saat menyimpan data login', 'error');
        }
    } finally {
        isProcessingLogin = false;
    }
}

// Function removed - login now handled through web page

async function handleLogout() {
    try {
        // Show logout loading
        showFullPageLoading(true, 'Melakukan logout...', 'Membersihkan data sesi');
        
        // Call logout API if token exists
        if (currentToken) {
            try {
                showFullPageLoading(true, 'Logout dari server...', 'Menghubungi API server');
                await fetch(`${API_BASE_URL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
            } catch (apiError) {
                console.log('API logout failed, continuing with local logout:', apiError);
            }
        }
        
        // Send logout message to background script to clear cached data
        showFullPageLoading(true, 'Membersihkan cache...', 'Menghapus data tersimpan');
        
        try {
            chrome.runtime.sendMessage({
                action: 'logout'
            }, (response) => {
                if (response && response.success) {
                    console.log('Cached data cleared successfully');
                } else {
                    console.error('Error clearing cached data:', response?.error);
                }
            });
        } catch (bgError) {
            console.log('Background script message failed:', bgError);
        }
        
        // Add small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Clear stored authentication
        await clearStoredAuth();
        
        // Reset global variables
        currentUser = null;
        currentToken = null;
        availableSites = [];
        
        // Reset initialization flags
        isInitialized = false;
        isProcessingLogin = false;
        
        // Show completion message
        showFullPageLoading(true, 'Logout berhasil!', 'Mengarahkan ke halaman login');
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Show login screen
        showLoginScreen();
        showFullPageLoading(false);
        showStatus('Logout berhasil, cache dibersihkan', 'success');
        
    } catch (error) {
        console.error('Logout error:', error);
        showFullPageLoading(false);
        showStatus('Terjadi kesalahan saat logout', 'error');
    }
}

async function getCurrentUser() {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
            'Authorization': `Bearer ${currentToken}`,
            'Accept': 'application/json'
        }
    });
    
    if (!response.ok) {
        throw new Error('Failed to get user info');
    }
    
    const data = await response.json();
    return data.user;
}

async function loadCategories() {
    // Extract categories from available sites instead of API call
    const categoriesSet = new Set();
    const categoriesMap = new Map();
    
    availableSites.forEach(site => {
        if (site.category_model && site.category_model.slug) {
            const category = site.category_model;
            if (!categoriesMap.has(category.slug)) {
                categoriesMap.set(category.slug, {
                    id: category.id,
                    name: category.name,
                    slug: category.slug,
                    color: category.color
                });
            }
        } else if (site.category) {
            // Fallback for old category format
            const categorySlug = site.category;
            if (!categoriesMap.has(categorySlug)) {
                categoriesMap.set(categorySlug, {
                    id: categorySlug,
                    name: categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1),
                    slug: categorySlug
                });
            }
        }
    });
    
    // Convert map to array
    availableCategories = Array.from(categoriesMap.values());
    
    // Sort categories by name
    availableCategories.sort((a, b) => a.name.localeCompare(b.name));
    
    renderFilterTabs();
}

function renderFilterTabs() {
    const filterTabsContainer = document.getElementById('filterTabs');
    if (!filterTabsContainer) return;
    
    // Clear existing tabs except "Semua"
    filterTabsContainer.innerHTML = '<button class="filter-tab active" data-category="all">Semua</button>';
    
    // Add category tabs
    availableCategories.forEach(category => {
        const tab = document.createElement('button');
        tab.className = 'filter-tab';
        tab.dataset.category = category.slug;
        tab.textContent = category.name;
        filterTabsContainer.appendChild(tab);
    });
    
    // Setup event listeners for new tabs
    setupFilterTabListeners();
}

function setupFilterTabListeners() {
    const tabs = document.querySelectorAll('.filter-tab');
    tabs.forEach(tab => {
        // Remove existing listeners
        tab.replaceWith(tab.cloneNode(true));
    });
    
    // Add new listeners
    const newTabs = document.querySelectorAll('.filter-tab');
    newTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            // Remove active class from all tabs
            newTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            e.target.classList.add('active');
            // Filter sites
            const category = e.target.dataset.category;
            const searchTerm = searchInput ? searchInput.value : '';
            renderSites(category, searchTerm);
        });
    });
}

async function loadSites() {
    // Set timeout for auto-logout after 1 minute
    const timeoutId = setTimeout(() => {
        showFullPageLoading(false);
        showStatus('Timeout: Proses memuat data terlalu lama. Logout otomatis...', 'error');
        setTimeout(() => {
            handleLogout();
        }, 2000);
    }, 60000); // 60 seconds
    
    try {
        const response = await fetch(`${API_BASE_URL}/sites`, {
            headers: {
                'Authorization': `Bearer ${currentToken}`,
                'Accept': 'application/json'
            }
        });
        
        // Clear timeout if request succeeds
        clearTimeout(timeoutId);
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                availableSites = data.sites || [];
                
                // Update loading message while rendering
                showFullPageLoading(true, 'Menyiapkan daftar situs...', `Menampilkan ${availableSites.length} situs tersedia`);
                
                // Add small delay to show the message
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // Load categories and render sites
                await loadCategories();
                renderSites();
                
                // Hide loading after sites are rendered
                showFullPageLoading(false);
                
                showStatus(`${availableSites.length} situs berhasil dimuat`, 'success');
            } else {
                showFullPageLoading(false);
                showStatus('Gagal memuat daftar situs', 'error');
            }
        } else if (response.status === 403) {
            // Handle subscription expired
            const data = await response.json();
            if (data.subscription_expired) {
                showFullPageLoading(true, 'Akun tidak aktif...', 'Masa aktif akun telah berakhir');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                await clearStoredAuth();
                currentToken = null;
                currentUser = null;
                showFullPageLoading(false);
                showLoginScreen();
                showLoginStatus('Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.', 'error');
                return;
            } else {
                showFullPageLoading(false);
                showStatus('Akses ditolak', 'error');
            }
        } else {
            showFullPageLoading(false);
            showStatus('Gagal memuat daftar situs', 'error');
        }
    } catch (error) {
        // Clear timeout if request fails
        clearTimeout(timeoutId);
        showFullPageLoading(false);
        console.error('Error loading sites:', error);
        showStatus('Error memuat situs', 'error');
    }
}

function renderSites(filterCategory = 'all', searchTerm = '') {
    if (!sitesList) return;
    
    sitesList.innerHTML = '';
    
    if (availableSites.length === 0) {
        sitesList.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.6); padding: 40px; grid-column: 1 / -1;">Tidak ada aplikasi tersedia</div>';
        return;
    }
    
    let filteredSites = availableSites;
    
    // Filter by category
    if (filterCategory !== 'all') {
        filteredSites = availableSites.filter(site => {
            // Check if site has categoryModel relationship
            if (site.category_model && site.category_model.slug) {
                return site.category_model.slug === filterCategory;
            }
            // Fallback to old category field
            return site.category === filterCategory;
        });
    }
    
    // Filter by search term
    if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase();
        filteredSites = filteredSites.filter(site => {
            return site.name.toLowerCase().includes(searchLower) ||
                   site.domain.toLowerCase().includes(searchLower) ||
                   (site.description && site.description.toLowerCase().includes(searchLower));
        });
    }
    
    if (filteredSites.length === 0) {
        const message = searchTerm.trim() !== '' ? 
            `Tidak ada aplikasi yang ditemukan untuk "${searchTerm}"` :
            'Tidak ada aplikasi untuk kategori ini';
        sitesList.innerHTML = `<div style="text-align: center; color: rgba(255,255,255,0.6); padding: 40px; grid-column: 1 / -1;">${message}</div>`;
        return;
    }
    
    filteredSites.forEach(site => {
        const siteCard = document.createElement('div');
        siteCard.className = 'site-card';
        siteCard.dataset.category = site.category;
        
        // Generate logo based on site name
        const logoContent = getSiteLogo(site);
        
        // Get category badge
        let categoryBadge = '';
        if (site.category_model) {
            categoryBadge = `<div class="site-category-badge" style="background-color: ${site.category_model.color || '#6366f1'}">${site.category_model.name}</div>`;
        } else {
            categoryBadge = `<div class="site-category-badge ${site.category}"></div>`;
        }
        
        siteCard.innerHTML = `
            <div class="site-logo">${logoContent}</div>
            <div class="site-name">${site.name}</div>
            ${categoryBadge}
        `;
        
        siteCard.addEventListener('click', () => openSiteWithCookies(site));
        sitesList.appendChild(siteCard);
    });
}

function getSiteLogo(site) {
    // Priority 1: Use thumbnail from database if available
    if (site.thumbnail && site.thumbnail.trim() !== '') {
        // Construct full URL if thumbnail is a relative path
        const thumbnailUrl = site.thumbnail.startsWith('http') ? 
            site.thumbnail : 
            `${API_BASE_URL.replace('/api', '/storage')}/${site.thumbnail.replace(/^\//, '')}`;
        
        return `<img src="${thumbnailUrl}" alt="${site.name}" onerror="this.onerror=null; this.parentElement.innerHTML=getSiteLogoFallback('${site.name}', '${site.url}');">`;
    }
    
    // Priority 2: Try to get favicon from URL or use fallback
    return getSiteLogoFallback(site.name, site.url);
}

function getSiteLogoFallback(siteName, siteUrl) {
    const domain = new URL(siteUrl).hostname;
    const firstLetter = siteName.charAt(0).toUpperCase();
    
    // Common site logos mapping with better emojis
    const logoMap = {
        'youtube.com': '📺',
        'netflix.com': '🎬',
        'spotify.com': '🎵',
        'github.com': '💻',
        'google.com': '🔍',
        'facebook.com': '📘',
        'twitter.com': '🐦',
        'instagram.com': '📷',
        'linkedin.com': '💼',
        'amazon.com': '🛒',
        'apple.com': '🍎',
        'microsoft.com': '🪟',
        'discord.com': '💬',
        'reddit.com': '🤖',
        'twitch.tv': '🎮',
        'tiktok.com': '🎭',
        'whatsapp.com': '💬',
        'telegram.org': '✈️',
        'zoom.us': '📹',
        'dropbox.com': '📦',
        'canva.com': '🎨',
        'figma.com': '🎨',
        'notion.so': '📝',
        'slack.com': '💼'
    };
    
    // Check if we have a custom emoji for this domain
    for (const [siteDomain, emoji] of Object.entries(logoMap)) {
        if (domain.includes(siteDomain)) {
            return `<div style="font-size: 32px; font-weight: bold;">${emoji}</div>`;
        }
    }
    
    // Try to load favicon with better fallback
    const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
    return `<img src="${faviconUrl}" alt="${siteName}" onerror="this.onerror=null; this.parentElement.innerHTML='<div style=\"font-size: 28px; font-weight: bold; color: rgba(255,255,255,0.9); text-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${firstLetter}</div>';">`;
}

async function openSiteWithCookies(site) {
    try {
        console.log('🚀 MANUAL COOKIE IMPORT - Opening site with cookies:', site);
        console.log('🔄 Auto-inject disabled - importing cookies only on manual click');
        
        // Validate site data
        if (!site || !site.id || !site.url) {
            throw new Error('Invalid site data');
        }
        
        // Show initial loading
        showFullPageLoading(true, `Menyiapkan ${site.name}...`, 'Menyiapkan aplikasi');
        showStatus(`Memuat data untuk ${site.name}...`, 'success');
        
        let cookiesData = null;
        
        // First try to get cookies from cache via background script
        try {
            const cachedCookiesResponse = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'getCachedCookiesForSite',
                    siteId: site.id
                }, resolve);
            });
            
            if (cachedCookiesResponse && cachedCookiesResponse.success && cachedCookiesResponse.data) {
                console.log(`Using cached cookies for site: ${site.name} (${cachedCookiesResponse.data.cookies.length} cookies)`);
                cookiesData = {
                    success: true,
                    data: {
                        cookies: cachedCookiesResponse.data.cookies,
                        site: {
                            id: cachedCookiesResponse.data.siteId,
                            name: cachedCookiesResponse.data.siteName,
                            domain: cachedCookiesResponse.data.domain
                        }
                    }
                };
            }
        } catch (error) {
            console.warn('Error getting cached cookies:', error);
        }
        
        // Fallback to API if not in cache
        if (!cookiesData) {
            console.log('Fetching cookies from API for site:', site.name);
            showFullPageLoading(true, `Mengambil data ${site.name}...`, 'Menyiapkan aplikasi');
            
            try {
                const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ domain: site.domain })
                });
                
                if (response.ok) {
                    cookiesData = await response.json();
                    console.log('Fetched cookies from API:', cookiesData);
                } else {
                    console.warn('Failed to fetch cookies from API, proceeding without cookies');
                    cookiesData = { success: true, data: { cookies: [] } };
                }
            } catch (apiError) {
                console.warn('API error, proceeding without cookies:', apiError);
                cookiesData = { success: true, data: { cookies: [] } };
            }
        }
        
        if (!cookiesData || !cookiesData.success) {
            console.warn('Invalid cookies response, proceeding without cookies');
            cookiesData = { success: true, data: { cookies: [] } };
        }
        
        // Combine site info with cookies data
        const siteData = {
            ...site,
            cookies: cookiesData.data ? (cookiesData.data.cookies || cookiesData.data) : []
        };
        
        // Validate and clean cookies data
        if (siteData.cookies && Array.isArray(siteData.cookies)) {
            siteData.cookies = siteData.cookies.filter(cookie => {
                // Basic validation
                if (!cookie.name || cookie.value === undefined) {
                    console.warn('Skipping invalid cookie (missing name or value):', cookie);
                    return false;
                }
                
                // Ensure domain is set
                if (!cookie.domain && siteData.domain) {
                    cookie.domain = siteData.domain;
                    console.log(`Set domain for cookie ${cookie.name}: ${cookie.domain}`);
                }
                
                return true;
            });
        }
        
        console.log('Site data with validated cookies:', siteData);
        
        if (siteData.cookies && Array.isArray(siteData.cookies) && siteData.cookies.length > 0) {
            console.log(`🍪 Starting direct cookie overwrite for ${siteData.name} (${siteData.cookies.length} cookies)`);
            showFullPageLoading(true, `Menyiapkan aplikasi...`, `Memproses ${siteData.cookies.length} data untuk ${siteData.name}`);
            showStatus(`Memproses ${siteData.cookies.length} data untuk ${siteData.name}...`, 'success');
            
            // DISABLED: Cookie clearing to prevent looping
            // Using direct overwrite strategy instead
            console.log('🔄 Using direct overwrite strategy - no clearing needed');
            console.log('📋 Cookies to set:', siteData.cookies);
            
            // Set cookies
            let successCount = 0;
            let errorCount = 0;
            
            // Try to use background script for better performance
            try {
                console.log('📤 Sending cookies to background script for bulk setting...');
                const response = await chrome.runtime.sendMessage({
                    action: 'setCookies',
                    cookies: siteData.cookies,
                    domain: siteData.domain
                });
                
                if (response && response.success) {
                    successCount = response.result.success;
                    errorCount = response.result.failed;
                    console.log('✅ Bulk cookie setting result:', response.result);
                } else {
                    throw new Error(response?.error || 'Background script failed');
                }
            } catch (error) {
                console.warn('⚠️ Background script failed, falling back to individual setting:', error);
                
                // Fallback to individual cookie setting
                console.log('🔄 Using fallback method - setting cookies individually...');
                for (let i = 0; i < siteData.cookies.length; i++) {
                    const cookie = siteData.cookies[i];
                    console.log(`🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`, cookie);
                    
                    try {
                        await setCookieWithExtensionAPI(cookie, siteData.domain);
                        successCount++;
                        console.log(`✅ Cookie ${cookie.name} set successfully`);
                    } catch (error) {
                        errorCount++;
                        console.error('❌ Cookie set error:', error);
                        console.warn('📋 Cookie data:', cookie);
                    }
                }
            }
            
            // Show detailed results
            if (successCount > 0) {
                const statusMessage = `${successCount}/${siteData.cookies.length} data berhasil diproses${errorCount > 0 ? ` (${errorCount} gagal)` : ''}. Membuka ${siteData.name}...`;
                showStatus(statusMessage, errorCount > 0 ? 'warning' : 'success');
                console.log(`🎯 Manual cookie import completed: ${successCount} success, ${errorCount} failed`);
            } else {
                showStatus(`Gagal memproses semua data. Membuka ${siteData.name} tanpa data tambahan...`, 'error');
                console.error('❌ All cookies failed to set');
            }
            
            // Add small delay before opening site to ensure cookies are properly set
            console.log('⏳ Waiting 300ms before opening site to ensure cookies are set...');
            await new Promise(resolve => setTimeout(resolve, 300));
        } else {
            showStatus(`Membuka ${siteData.name}...`, 'success');
            console.log('ℹ️ No cookies to set for this site - opening directly');
        }
        
        // Open site in new tab
        showFullPageLoading(true, `Membuka ${siteData.name}...`, 'Menyiapkan tab baru');
        
        try {
            console.log(`🌐 Opening site in new tab: ${siteData.url}`);
            chrome.tabs.create({ url: siteData.url });
            console.log('✅ Site opened successfully');
            
            // Add small delay to show completion
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            console.warn('⚠️ Chrome tabs API error:', error);
            // Fallback: open in current window
            console.log('🔄 Using fallback method to open site...');
            window.open(siteData.url, '_blank');
        }
        
        // Hide loading
        showFullPageLoading(false);
        
    } catch (error) {
        console.error('❌ Manual cookie import error:', error);
        
        // Hide loading on error
        showFullPageLoading(false);
        
        if (error.message && error.message.includes('Extension context invalidated')) {
            console.error('🔄 Extension context invalidated - reload required');
            showStatus('Ekstensi perlu di-reload untuk membuka situs.', 'error');
            showTroubleshooting();
        } else {
            console.error(`💥 Unexpected error: ${error.message}`);
            showStatus(`Error: ${error.message}`, 'error');
        }
    }
}

async function setCookieWithExtensionAPI(cookie, siteDomain) {
    return new Promise((resolve, reject) => {
        // Validate required fields
        if (!cookie.name || cookie.value === undefined) {
            reject(new Error('Cookie name and value are required'));
            return;
        }
        
        // Clean siteDomain (remove protocol if present)
        const cleanSiteDomain = siteDomain.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
        
        // Determine protocol based on secure flag
        const protocol = (cookie.secure !== false) ? 'https' : 'http';
        
        // Prepare cookie for Chrome API
        const cookieDetails = {
            url: `${protocol}://${cleanSiteDomain}`,
            name: cookie.name,
            value: cookie.value
        };
        
        console.log(`Using site domain: ${cleanSiteDomain}, protocol: ${protocol}`);
        
        // Handle domain - prioritize cookie.domain, fallback to siteDomain
        if (cookie.domain) {
            // Clean domain (remove protocol if present)
            let cleanDomain = cookie.domain.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
            
            // If domain doesn't start with dot and is not the exact siteDomain, add dot for subdomain support
             if (!cleanDomain.startsWith('.') && cleanDomain !== cleanSiteDomain) {
                 cookieDetails.domain = `.${cleanDomain}`;
             } else {
                 cookieDetails.domain = cleanDomain;
             }
         } else {
             cookieDetails.domain = cleanSiteDomain;
         }
        
        console.log(`Domain mapping: ${cookie.domain || 'undefined'} -> ${cookieDetails.domain}`);
        
        // Handle path
        cookieDetails.path = cookie.path || '/';
        
        // Handle secure flag
        if (cookie.secure !== undefined) {
            cookieDetails.secure = cookie.secure;
        } else {
            // Default to true for HTTPS sites
            cookieDetails.secure = true;
        }
        
        // Handle httpOnly flag
        if (cookie.httpOnly !== undefined) {
            cookieDetails.httpOnly = cookie.httpOnly;
        }
        
        // Handle sameSite
        if (cookie.sameSite) {
            const validSameSite = ['unspecified', 'no_restriction', 'lax', 'strict'];
            if (validSameSite.includes(cookie.sameSite)) {
                cookieDetails.sameSite = cookie.sameSite;
            } else if (cookie.sameSite === 'none') {
                cookieDetails.sameSite = 'no_restriction';
            } else {
                // Default mapping for common values
                cookieDetails.sameSite = 'lax';
            }
        }
        
        // Handle expiration date
        if (cookie.expirationDate) {
            // Handle both Unix timestamp and Date object
            if (typeof cookie.expirationDate === 'number') {
                cookieDetails.expirationDate = cookie.expirationDate;
            } else if (cookie.expirationDate instanceof Date) {
                cookieDetails.expirationDate = Math.floor(cookie.expirationDate.getTime() / 1000);
            }
        } else {
            // Set expiration to 1 year from now if not specified
            cookieDetails.expirationDate = Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60);
        }
        
        console.log('Setting cookie:', cookieDetails);
        
        // Function to attempt setting cookie
        const attemptSetCookie = (details, isRetry = false) => {
            try {
                chrome.cookies.set(details, (result) => {
                    if (chrome.runtime.lastError) {
                        console.warn('Cookie set error:', chrome.runtime.lastError.message);
                        console.warn('Cookie details:', details);
                        
                        // If HTTPS failed and this is not a retry, try with HTTP
                        if (!isRetry && details.url.startsWith('https://')) {
                            console.log('Retrying with HTTP...');
                            const httpDetails = { ...details };
                            httpDetails.url = details.url.replace('https://', 'http://');
                            httpDetails.secure = false;
                            attemptSetCookie(httpDetails, true);
                        } else {
                            reject(new Error(chrome.runtime.lastError.message));
                        }
                    } else {
                        console.log('Cookie set successfully:', result);
                        resolve(result);
                    }
                });
            } catch (error) {
                console.warn('Chrome cookies API error:', error);
                reject(new Error('Extension context invalidated'));
            }
        };
        
        // Start the attempt
        attemptSetCookie(cookieDetails);
    });
}

function showLoginScreen() {
    // Ensure loading is hidden when showing login screen
    showFullPageLoading(false);
    loginScreen.style.display = 'block';
    mainScreen.style.display = 'none';
}

function showMainScreen() {
    loginScreen.style.display = 'none';
    mainScreen.style.display = 'block';
    
    // Update user info
    userName.textContent = currentUser.name;
    userRole.textContent = currentUser.role;
    userRole.className = `role-badge ${currentUser.role}`;
    
    // Update app version
    updateAppVersion();
}

function showLoginStatus(message, type) {
    loginStatus.textContent = message;
    loginStatus.className = `status ${type}`;
    loginStatus.style.display = 'block';
    
    setTimeout(() => {
        loginStatus.style.display = 'none';
    }, 3000);
}

function showStatus(message, type) {
    if (statusDiv) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.style.display = 'block';
        
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

function showTroubleshooting() {
    if (troubleshootingDiv) {
        troubleshootingDiv.style.display = 'block';
    }
}

function hideTroubleshooting() {
    if (troubleshootingDiv) {
        troubleshootingDiv.style.display = 'none';
    }
}

// Update UI based on authentication status
function updateUI() {
    if (currentUser && currentToken) {
        // Show main screen
        if (loginScreen) loginScreen.style.display = 'none';
        if (mainScreen) mainScreen.style.display = 'block';
        
        // Update user info
        if (userName) userName.textContent = currentUser.name;
        if (userRole) {
            userRole.textContent = currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
            userRole.className = `role-badge ${currentUser.role}`;
        }
        
        // Update subscription info
        updateSubscriptionInfo();
        
        // Update app version
        updateAppVersion();
        
        // Load sites
        loadSites();
    } else {
        // Show login screen
        if (loginScreen) loginScreen.style.display = 'block';
        if (mainScreen) mainScreen.style.display = 'none';
    }
}

function updateSubscriptionInfo() {
    if (!currentUser || !subscriptionInfo) return;
    
    if (currentUser.subscription_expires_at) {
        const expiryDate = new Date(currentUser.subscription_expires_at);
        const now = new Date();
        const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
        
        let subscriptionText = '';
        let className = '';
        
        if (daysLeft < 0) {
            subscriptionText = 'Langganan berakhir';
            className = 'danger';
        } else if (daysLeft <= 7) {
            subscriptionText = `${daysLeft} hari lagi`;
            className = 'warning';
        } else {
            subscriptionText = `${daysLeft} hari lagi`;
            className = '';
        }
        
        subscriptionInfo.textContent = subscriptionText;
        subscriptionInfo.className = `subscription-info ${className}`;
    } else {
        subscriptionInfo.textContent = 'Unlimited';
        subscriptionInfo.className = 'subscription-info';
    }
}

function updateAppVersion() {
    if (!appVersion) return;
    
    // Get version from manifest
    const manifest = chrome.runtime.getManifest();
    if (manifest && manifest.version) {
        appVersion.textContent = `Versi ${manifest.version}`;
    }
}

// Storage functions
async function saveAuth(authData) {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.set({ satuPintuAuth: authData }, () => {
                if (chrome.runtime.lastError) {
                    console.warn('Storage save error:', chrome.runtime.lastError.message);
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve();
                }
            });
        } catch (error) {
            console.warn('Chrome storage API error:', error);
            reject(new Error('Extension context invalidated'));
        }
    });
}

async function getStoredAuth() {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.get(['satuPintuAuth'], (result) => {
                if (chrome.runtime.lastError) {
                    console.warn('Storage get error:', chrome.runtime.lastError.message);
                    resolve(null); // Return null instead of rejecting for get operations
                } else {
                    resolve(result.satuPintuAuth || null);
                }
            });
        } catch (error) {
            console.warn('Chrome storage API error:', error);
            resolve(null); // Return null instead of rejecting for get operations
        }
    });
}

async function clearStoredAuth() {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.remove(['satuPintuAuth'], () => {
                if (chrome.runtime.lastError) {
                    console.warn('Storage clear error:', chrome.runtime.lastError.message);
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve();
                }
            });
        } catch (error) {
            console.warn('Chrome storage API error:', error);
            reject(new Error('Extension context invalidated'));
        }
    });
}

// Full page loading animation
function showFullPageLoading(show, message = 'Memuat data situs...', subtext = 'Mohon tunggu sebentar') {
    if (fullPageLoader) {
        if (show) {
            // Update loading text
            const loaderText = fullPageLoader.querySelector('.loader-text');
            const loaderSubtext = fullPageLoader.querySelector('.loader-subtext');
            if (loaderText) loaderText.textContent = message;
            if (loaderSubtext) loaderSubtext.textContent = subtext;
            
            fullPageLoader.style.display = 'flex';
            // Disable all interactive elements
            if (loginBtn) loginBtn.disabled = true;
            if (logoutBtn) logoutBtn.disabled = true;
            if (emailInput) emailInput.disabled = true;
            if (passwordInput) passwordInput.disabled = true;
        } else {
            fullPageLoader.style.display = 'none';
            // Re-enable interactive elements
            if (loginBtn) loginBtn.disabled = false;
            if (logoutBtn) logoutBtn.disabled = false;
            if (emailInput) emailInput.disabled = false;
            if (passwordInput) passwordInput.disabled = false;
        }
    }
}

// Get cached data from background script
async function getCachedData() {
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({
            action: "getCachedData"
        }, (response) => {
            if (response && response.userSession) {
                console.log('Retrieved cached session:', response.userSession.sessionId);
                console.log('Cached sites count:', response.sitesData?.data?.length || 0);
                console.log('Cached cookies files:', Object.keys(response.cookiesFolder || {}).length);
                resolve(response);
            } else {
                resolve(null);
            }
        });
    });
}
