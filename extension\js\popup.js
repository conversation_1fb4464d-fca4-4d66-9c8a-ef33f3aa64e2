// Configuration
// const API_BASE_URL = "https://satupintu.lik.my.id/api";
const API_BASE_URL = "http://127.0.0.1:8000/api/v2";

// Keep service worker alive while popup is open
let backgroundPort;

function connectToBackground() {
  try {
    backgroundPort = chrome.runtime.connect({ name: 'popup' });
    console.log('🔗 Connected to background service worker');

    backgroundPort.onDisconnect.addListener(() => {
      console.log('🔌 Disconnected from background service worker');
      console.warn('⚠️ Service worker may have been suspended or crashed');
      // Attempt to reconnect if needed
      setTimeout(() => {
        if (!backgroundPort || backgroundPort.disconnected) {
          console.log('🔄 Attempting to reconnect to service worker...');
          connectToBackground();
        }
      }, 1000);
    });

  } catch (error) {
    console.error('❌ Failed to connect to background:', error);
  }
}
// Connect immediately when popup loads
connectToBackground();

// Cleanup on popup close
window.addEventListener('beforeunload', () => {
  if (backgroundPort) {
    backgroundPort.disconnect();
  }
});

/**
 * SELECTOR SUPPORT DOCUMENTATION
 * ==============================
 *
 * Ekstensi ini mendukung berbagai jenis selector untuk menemukan elemen login:
 *
 * 1. CSS SELECTORS:
 *    - ID: #mui-23, #email-input
 *    - Class: .email-field, .login-button
 *    - Attribute: [name="email"], [type="password"]
 *    - Complex: input[type="email"][name="username"]
 *
 * 2. XPATH SELECTORS (RECOMMENDED untuk Material-UI):
 *    - Basic: //*[@id="mui-23"]
 *    - Type-based: //input[@type="email"]
 *    - Contains: //input[contains(@id, "email")]
 *    - Text content: //button[contains(text(), "Login")]
 *    - Multiple conditions: //input[@type="email" and contains(@class, "MuiInput")]
 *
 * 3. AUTO ID DETECTION:
 *    - Input: mui-23 → Automatically converts to #mui-23
 *    - Input: email-field → Automatically converts to #email-field
 *
 * CONTOH PENGGUNAAN XPATH UNTUK MATERIAL-UI:
 * - Email field: //*[@id="mui-23"] atau //input[contains(@id, "mui") and @type="email"]
 * - Password field: //*[@id="mui-24"] atau //input[contains(@id, "mui") and @type="password"]
 * - Submit button: //button[contains(text(), "Login")] atau //button[@type="submit"]
 *
 * TIPS:
 * - XPath lebih fleksibel untuk elemen dengan ID dinamis
 * - Gunakan contains() untuk ID yang berubah-ubah
 * - Kombinasikan multiple conditions untuk akurasi lebih tinggi
 */

// Import components
import importantInfo from "./components/important-info.js";
import siteDetail from "./components/site-detail.js";

// Global variables
let currentUser = null;
let currentToken = null;
let availableSites = [];
let isInitializing = false;
let isInitialized = false;
let isProcessingLogin = false;
let initTimeout = null;
let importantInfoData = null;

// DOM elements
let loginForm,
  loginBtn,
  emailInput,
  passwordInput,
  logoutBtn,
  loginScreen,
  mainScreen,
  userInfo,
  sitesList,
  statusDiv,
  userName,
  userRole,
  loginStatus,
  troubleshootingDiv,
  fullPageLoader,
  subscriptionInfo,
  filterTabs,
  searchInput,
  appVersion;

// Categories data
let availableCategories = [];

// Initialize when DOM is loaded with debouncing
function debouncedInit() {
  if (initTimeout) {
    clearTimeout(initTimeout);
  }
  initTimeout = setTimeout(init, 100); // 100ms debounce
}

// Enable smooth scrolling for sites section
function enableSmoothScrolling() {
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    // Ensure the sites section is scrollable
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
    sitesSection.style.flex = "1";

    // Add wheel event listener for smooth scrolling
    sitesSection.addEventListener("wheel", function (e) {
      // Prevent the event from propagating to parent elements
      e.stopPropagation();
    });

    // Force layout recalculation
    sitesSection.style.display = "none";
    setTimeout(() => {
      sitesSection.style.display = "block";

      // Add MutationObserver to ensure scrolling works when content changes
      const observer = new MutationObserver(() => {
        sitesSection.style.overflowY = "auto";
      });

      observer.observe(sitesSection, { childList: true, subtree: true });
    }, 0);
  }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Get DOM elements
  loginForm = document.getElementById("loginForm");
  loginBtn = document.getElementById("loginBtn");
  emailInput = document.getElementById("email");
  passwordInput = document.getElementById("password");
  logoutBtn = document.getElementById("logoutBtn");
  loginScreen = document.getElementById("loginScreen");
  mainScreen = document.getElementById("mainScreen");
  userInfo = document.getElementById("userInfo");
  sitesList = document.getElementById("sitesList");
  statusDiv = document.getElementById("status");
  userName = document.getElementById("userName");
  userRole = document.getElementById("userRole");
  loginStatus = document.getElementById("loginStatus");
  troubleshootingDiv = document.getElementById("troubleshooting");
  fullPageLoader = document.getElementById("fullPageLoader");
  subscriptionInfo = document.getElementById("subscriptionInfo");
  filterTabs = document.querySelectorAll(".filter-tab");
  searchInput = document.getElementById("searchInput");
  appVersion = document.getElementById("appVersion");

  // Tambahkan event listener untuk efek ripple pada tombol login
  if (loginBtn) {
    loginBtn.addEventListener("mousedown", function (e) {
      const ripple = loginBtn.querySelector(".btn-ripple");
      if (ripple) {
        // Reset animasi
        ripple.style.animation = "none";
        ripple.offsetHeight; // Trigger reflow

        // Posisikan ripple pada posisi klik
        const rect = loginBtn.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ripple.style.top = y + "px";
        ripple.style.left = x + "px";

        // Mulai animasi
        ripple.style.animation = "ripple 0.6s ease-out forwards";
      }
    });
  }

  // Fix scrolling issues
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    // Prevent scrolling on body when scrolling in sites section
    sitesSection.addEventListener("wheel", function (e) {
      e.stopPropagation();
    });
  }

  // Add event listeners
  if (loginForm) {
    loginForm.addEventListener("submit", function (e) {
      // Trigger efek ripple pada tombol login saat form disubmit
      const ripple = loginBtn.querySelector(".btn-ripple");
      if (ripple) {
        // Reset animasi
        ripple.style.animation = "none";
        ripple.offsetHeight; // Trigger reflow

        // Posisikan ripple di tengah tombol
        const rect = loginBtn.getBoundingClientRect();
        ripple.style.top = rect.height / 2 + "px";
        ripple.style.left = rect.width / 2 + "px";

        // Mulai animasi
        ripple.style.animation = "ripple 0.6s ease-out forwards";
      }

      // Panggil fungsi handleLogin
      handleLogin(e);
    });
  }

  if (logoutBtn) {
    logoutBtn.addEventListener("click", handleLogout);
  }

  // Add search input event listener
  if (searchInput) {
    searchInput.addEventListener("input", (e) => {
      const searchTerm = e.target.value;
      const activeTab = document.querySelector(".filter-tab.active");
      const category = activeTab ? activeTab.dataset.category : "all";
      renderSites(category, searchTerm);
    });
  }

  // Add filter tab event listeners (will be updated when categories are loaded)
  if (document.getElementById("filterTabs")) {
    setupFilterTabListeners();
  }

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "loginSuccessFromBackground") {
      console.log(
        "Popup received login success from background:",
        request.authData
      );
      console.log("Current processing state:", {
        isProcessingLogin,
        isInitialized,
      });
      handleLoginSuccess(request.authData.token, request.authData.user);
      sendResponse({ success: true });
    }
  });

  // Initialize with debouncing
  debouncedInit();

  // Enable smooth scrolling
  enableSmoothScrolling();

  // Setup footer handlers
  setupFooterHandlers();
});

async function init() {
  // Clear any pending init timeout
  if (initTimeout) {
    clearTimeout(initTimeout);
    initTimeout = null;
  }

  // Prevent multiple initialization
  if (isInitializing || isInitialized) {
    console.log("Init already in progress or completed, skipping...");
    return;
  }

  // Set popup size
  try {
    document.body.style.width = "420px";
    document.body.style.height = "600px";
    document.body.style.minWidth = "420px";
    document.body.style.minHeight = "600px";
    document.body.style.maxWidth = "420px";
    document.body.style.maxHeight = "600px";
    document.body.style.overflow = "hidden"; // Ensure body doesn't scroll
  } catch (error) {
    console.log("Could not set popup size:", error);
  }

  isInitializing = true;
  console.log("Initializing popup...", {
    timestamp: new Date().toISOString(),
    flags: { isInitializing, isInitialized, isProcessingLogin },
  });

  try {
    showFullPageLoading(
      true,
      "Memulai aplikasi...",
      "Memeriksa koneksi server"
    );

    // Test server connection first
    try {
      console.log("Testing server connection to:", API_BASE_URL);
      const testResponse = await fetch(`${API_BASE_URL}/sites`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      });
      console.log(
        "Server connection test result:",
        testResponse.status,
        testResponse.statusText
      );
      if (!testResponse.ok && testResponse.status !== 401) {
        console.warn(
          "Server may not be running properly. Status:",
          testResponse.status
        );
      }
    } catch (connectionError) {
      console.error("Server connection test failed:", connectionError);
      showLoginStatus(
        "Tidak dapat terhubung ke server. Pastikan server Laravel berjalan di http://127.0.0.1:8000",
        "error"
      );
    }

    showFullPageLoading(true, "Memulai aplikasi...", "Memeriksa status login");

    // Clear any existing badge when popup opens
    try {
      await chrome.action.setBadgeText({ text: "" });
    } catch (error) {
      console.log("Could not clear badge:", error);
    }

    // Always check stored auth data to get fresh subscription info from server
    showFullPageLoading(
      true,
      "Memeriksa autentikasi...",
      "Memvalidasi token akses"
    );
    const storedAuth = await getStoredAuth();

    if (storedAuth && storedAuth.token) {
      console.log(
        "Found stored auth, checking status and refreshing subscription data"
      );
      await checkAuthStatus();
    } else {
      console.log("No stored auth found, showing login screen");
      showLoginScreen();
    }
  } catch (error) {
    console.error("Initialization error:", error);
    showFullPageLoading(
      true,
      "Terjadi kesalahan...",
      "Menampilkan halaman login"
    );
    await new Promise((resolve) => setTimeout(resolve, 1000));
    showLoginScreen();
  } finally {
    showFullPageLoading(false);
    isInitializing = false;
    isInitialized = true;
    console.log("Popup initialization completed successfully", {
      timestamp: new Date().toISOString(),
      flags: { isInitializing, isInitialized, isProcessingLogin },
    });
  }
}

// Check authentication status on popup load
async function checkAuthStatus() {
  try {
    const authData = await getStoredAuth();
    if (!authData || !authData.token) {
      showLoginScreen();
      return;
    }

    // Set current data from stored auth immediately
    currentToken = authData.token;
    currentUser = authData.user;

    // Show main screen immediately to avoid login screen flash
    showMainScreen();

    // Verify token with server in background
    try {
      showFullPageLoading(
        true,
        "Memverifikasi akses...",
        "Memeriksa token autentikasi"
      );

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${authData.token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Update user data with proper subscription mapping
          currentUser = {
            ...data.user,
            // Map nested subscription data to flat structure for compatibility
            subscription_expires_at: data.user.subscription?.expires_at,
            subscription_active: !data.user.subscription?.is_expired,
            subscription_status: data.user.subscription?.status,
            subscription_plan: data.user.subscription?.name,
          };
          showFullPageLoading(
            true,
            "Memuat daftar situs...",
            "Mengambil data dari server"
          );
          await loadSites();
          updateUI();
          return;
        }
      } else if (response.status === 403) {
        // Handle subscription expired
        const data = await response.json();
        if (data.subscription_expired) {
          showFullPageLoading(
            true,
            "Akun tidak aktif...",
            "Masa aktif akun telah berakhir"
          );
          await new Promise((resolve) => setTimeout(resolve, 2000));

          await clearStoredAuth();
          currentToken = null;
          currentUser = null;
          showFullPageLoading(false);
          showLoginScreen();
          showLoginStatus(
            "Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.",
            "error"
          );
          return;
        }
      }

      // Token is invalid, remove it and show login screen
      showFullPageLoading(
        true,
        "Token tidak valid...",
        "Mengarahkan ke halaman login"
      );
      await new Promise((resolve) => setTimeout(resolve, 1000));

      await clearStoredAuth();
      currentToken = null;
      currentUser = null;
      showFullPageLoading(false);
      showLoginScreen();
    } catch (verifyError) {
      console.error("Error verifying token:", verifyError);
      // Keep showing main screen with cached data if verification fails
      // This handles offline scenarios
      showFullPageLoading(
        true,
        "Mode offline...",
        "Menggunakan data tersimpan"
      );
      await new Promise((resolve) => setTimeout(resolve, 500));
      await loadSites();
      updateUI();
    }
  } catch (error) {
    console.error("Error checking auth status:", error);
    showFullPageLoading(
      true,
      "Terjadi kesalahan...",
      "Menampilkan halaman login"
    );
    await new Promise((resolve) => setTimeout(resolve, 1000));
    showFullPageLoading(false);
    showLoginScreen();
  }
}

async function handleLogin(event) {
  event.preventDefault(); // Mencegah form melakukan submit default

  console.log("handleLogin dipanggil");
  const email = emailInput.value.trim();
  const password = passwordInput.value.trim();

  if (!email || !password) {
    showLoginStatus("Mohon isi email dan password", "error");
    return;
  }

  // Show loading state
  setLoginLoading(true);

  try {
    // Menggunakan endpoint login v2
    const loginEndpoint = `${API_BASE_URL}/auth/login`;
    console.log("Mengirim permintaan login ke:", loginEndpoint);
    console.log("Data login:", { email, password: "[DISEMBUNYIKAN]" });

    const response = await fetch(loginEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        email: email,
        password: password,
      }),
    });

    console.log("Status respons:", response.status, response.statusText);
    console.log(
      "Response headers:",
      Object.fromEntries(response.headers.entries())
    );

    let data;
    try {
      const responseText = await response.text();
      console.log("Raw response:", responseText);
      data = JSON.parse(responseText);
      console.log("Parsed data:", data);
    } catch (parseError) {
      console.error("Failed to parse JSON response:", parseError);
      throw new Error(`Server returned invalid JSON: ${parseError.message}`);
    }

    if (response.ok && data.success) {
      // Save authentication data WITHOUT subscription info to prevent caching
      const userDataWithoutSubscription = {
        id: data.user.id,
        name: data.user.name,
        email: data.user.email,
        role: data.user.role,
        // Exclude all subscription-related fields from cache
      };

      // Set current user with subscription data for immediate use
      currentUser = {
        ...data.user,
        // Map nested subscription data to flat structure for compatibility
        subscription_expires_at: data.user.subscription?.expires_at,
        subscription_active: !data.user.subscription?.is_expired,
        subscription_status: data.user.subscription?.status,
        subscription_plan: data.user.subscription?.name,
      };
      currentToken = data.token;

      // Store in chrome storage WITHOUT subscription data
      try {
        await saveAuth({
          token: currentToken,
          user: userDataWithoutSubscription,
        });
      } catch (storageError) {
        console.warn("Failed to save auth data:", storageError);
      }

      // Clear form
      emailInput.value = "";
      passwordInput.value = "";

      // Hide troubleshooting section if visible
      hideTroubleshooting();

      // Show success message
      showLoginStatus(
        "Login berhasil! Selamat datang, " + data.user.name,
        "success"
      );

      // Notify background script
      try {
        chrome.runtime.sendMessage({
          action: "loginSuccess",
          authData: {
            token: currentToken,
            user: currentUser,
          },
          source: "popup_init",
        });
      } catch (e) {
        console.log("Could not send message to background:", e);
      }

      // Load sites and show main screen
      showFullPageLoading(
        true,
        "Login berhasil!",
        "Menyiapkan dashboard Anda..."
      );

      // Add small delay to show success message
      await new Promise((resolve) => setTimeout(resolve, 800));

      // Tambahkan animasi transisi saat login berhasil
      loginScreen.style.transition = "opacity 0.5s ease-out";
      loginScreen.style.opacity = "0";

      await new Promise((resolve) => setTimeout(resolve, 500));

      showMainScreen();

      // Tambahkan animasi fade-in untuk main screen
      mainScreen.style.opacity = "0";
      mainScreen.style.display = "flex";
      mainScreen.style.transition = "opacity 0.5s ease-in";

      // Trigger reflow untuk memastikan transisi berjalan
      mainScreen.offsetHeight;

      mainScreen.style.opacity = "1";

      await loadSites();

      // Hide loading after everything is ready
      showFullPageLoading(false);
    } else {
      // Handle different HTTP status codes
      let errorMessage =
        data.message || "Login gagal. Periksa email dan password Anda.";

      if (response.status === 401) {
        errorMessage = data.message || "Email atau password salah.";
      } else if (response.status === 403) {
        errorMessage =
          data.message || "Akses ditolak. Periksa status langganan Anda.";
      } else if (response.status === 422) {
        errorMessage =
          data.message ||
          "Data tidak valid. Mohon periksa email dan password Anda.";
      } else if (response.status === 500) {
        errorMessage =
          "Terjadi kesalahan pada server. Silakan coba lagi nanti.";
      } else if (response.status >= 400) {
        errorMessage = `Error ${response.status}: ${
          data.message || response.statusText
        }`;
      }

      showLoginStatus(errorMessage, "error");
      console.error(
        "Login failed with status:",
        response.status,
        "Message:",
        errorMessage
      );
    }
  } catch (error) {
    console.error("Login error:", error);
    console.error("Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    if (
      error.message &&
      error.message.includes("Extension context invalidated")
    ) {
      showLoginStatus("Ekstensi perlu di-reload untuk login.", "error");
      showTroubleshooting();
    } else if (
      error.name === "TypeError" &&
      error.message.includes("Failed to fetch")
    ) {
      showLoginStatus(
        "Gagal terhubung ke server. Periksa koneksi internet Anda atau pastikan server berjalan di http://127.0.0.1:8000",
        "error"
      );
      console.error("Kesalahan koneksi ke server:", error);
    } else if (error.name === "SyntaxError" && error.message.includes("JSON")) {
      showLoginStatus(
        "Server mengembalikan respons yang tidak valid. Periksa konfigurasi server.",
        "error"
      );
      console.error("JSON parsing error:", error);
    } else {
      showLoginStatus(
        `Terjadi kesalahan saat login: ${
          error.message || "Unknown error"
        }. Silakan coba lagi.`,
        "error"
      );
      console.error("Unexpected error:", error);
    }
  } finally {
    setLoginLoading(false);
  }
}

function setLoginLoading(isLoading) {
  if (loginBtn) {
    const btnText = loginBtn.querySelector(".btn-text");
    const btnLoading = loginBtn.querySelector(".btn-loading");

    if (isLoading) {
      btnText.style.display = "none";
      btnLoading.style.display = "inline-flex";
      loginBtn.disabled = true;
      loginBtn.style.pointerEvents = "none";
      emailInput.disabled = true;
      passwordInput.disabled = true;
    } else {
      btnText.style.display = "inline";
      btnLoading.style.display = "none";
      loginBtn.disabled = false;
      loginBtn.style.pointerEvents = "auto";
      emailInput.disabled = false;
      passwordInput.disabled = false;
    }
  }
}

// Handle login success from extension success page
async function handleLoginSuccess(token, user) {
  // Prevent multiple processing
  if (isProcessingLogin) {
    console.log("Login already being processed, ignoring duplicate call");
    return;
  }

  isProcessingLogin = true;

  try {
    console.log("handleLoginSuccess called with:", {
      token: token ? "present" : "missing",
      user: user ? "present" : "missing",
    });

    // Validate parameters
    if (!token) {
      throw new Error("Token parameter is undefined or empty");
    }

    if (!user) {
      throw new Error("User parameter is undefined or empty");
    }

    // Set current user with subscription data for immediate use
    currentUser = {
      ...user,
      // Map nested subscription data to flat structure for compatibility
      subscription_expires_at:
        user.subscription?.expires_at || user.subscription_expires_at,
      subscription_active:
        user.subscription?.is_expired === false || user.subscription_active,
      subscription_status:
        user.subscription?.status || user.subscription_status,
      subscription_plan: user.subscription?.name || user.subscription_plan,
      days_until_expiry: user.days_until_expiry,
    };
    currentToken = token;

    // Store in chrome storage WITHOUT subscription data to prevent caching
    try {
      const userDataWithoutSubscription = {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        // Exclude all subscription-related fields from cache
      };
      await saveAuth({
        token: currentToken,
        user: userDataWithoutSubscription,
      });
    } catch (storageError) {
      console.warn("Failed to save auth data:", storageError);
      // Continue anyway as we have the data in memory
    }

    // Note: loginSuccess message is now sent by content script, not popup
    // This prevents duplicate processing and looping issues
    console.log(
      "Login success handled by popup, waiting for background caching..."
    );

    // Hide troubleshooting section if visible
    hideTroubleshooting();

    // Show success message
    showStatus(
      "Login berhasil! Data disinkronkan. Selamat datang, " + user.name,
      "success"
    );

    // Load sites and show main screen
    showMainScreen();

    // Wait a shorter time for background script to finish caching
    setTimeout(async () => {
      try {
        // Try to get cached data first, fallback to API if needed
        const cachedData = await getCachedData();
        if (cachedData && cachedData.sitesData) {
          availableSites = cachedData.sitesData.data || [];
          renderSites();
          updateUI();
          console.log("Sites loaded from cache successfully");
        } else {
          console.log("No cached sites found, loading from API");
          await loadSites();
        }
      } catch (error) {
        console.error("Error loading sites after login:", error);
        await loadSites();
      }
    }, 500);
  } catch (error) {
    console.error("Error handling login success:", error);
    if (
      error.message &&
      error.message.includes("Extension context invalidated")
    ) {
      showStatus("Ekstensi perlu di-reload untuk login.", "error");
      showTroubleshooting();
    } else {
      showStatus("Error saat menyimpan data login", "error");
    }
  } finally {
    isProcessingLogin = false;
  }
}

// Function removed - login now handled through web page

async function handleLogout() {
  try {
    // Show logout loading
    showFullPageLoading(true, "Melakukan logout...", "Membersihkan data sesi");

    // Call logout API if token exists
    if (currentToken) {
      try {
        showFullPageLoading(
          true,
          "Logout dari server...",
          "Menghubungi API server"
        );
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${currentToken}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });
      } catch (apiError) {
        console.log(
          "API logout failed, continuing with local logout:",
          apiError
        );
      }
    }

    // Send logout message to background script to clear cached data
    showFullPageLoading(
      true,
      "Membersihkan cache...",
      "Menghapus data tersimpan"
    );

    try {
      chrome.runtime.sendMessage(
        {
          action: "logout",
        },
        (response) => {
          if (response && response.success) {
            console.log("Cached data cleared successfully");
          } else {
            console.error("Error clearing cached data:", response?.error);
          }
        }
      );
    } catch (bgError) {
      console.log("Background script message failed:", bgError);
    }

    // Add small delay to show progress
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Clear stored authentication
    await clearStoredAuth();

    // Reset global variables
    currentUser = null;
    currentToken = null;
    availableSites = [];

    // Reset initialization flags
    isInitialized = false;
    isProcessingLogin = false;

    // Show completion message
    showFullPageLoading(
      true,
      "Logout berhasil!",
      "Mengarahkan ke halaman login"
    );
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Tambahkan animasi transisi saat logout
    mainScreen.style.transition = "opacity 0.5s ease-out";
    mainScreen.style.opacity = "0";

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Show login screen
    showLoginScreen();
    showFullPageLoading(false);
    showStatus("Logout berhasil, cache dibersihkan", "success");
  } catch (error) {
    console.error("Logout error:", error);
    showFullPageLoading(false);
    showStatus("Terjadi kesalahan saat logout", "error");
  }
}

async function getCurrentUser() {
  const response = await fetch(`${API_BASE_URL}/auth/me`, {
    headers: {
      Authorization: `Bearer ${currentToken}`,
      Accept: "application/json",
    },
  });

  if (!response.ok) {
    throw new Error("Failed to get user info");
  }

  const data = await response.json();
  // Map nested subscription data to flat structure for compatibility
  return {
    ...data.user,
    subscription_expires_at: data.user.subscription?.expires_at,
    subscription_active: !data.user.subscription?.is_expired,
    subscription_status: data.user.subscription?.status,
    subscription_plan: data.user.subscription?.name,
  };
}

async function loadCategories() {
  // Extract categories from available sites instead of API call
  const categoriesSet = new Set();
  const categoriesMap = new Map();

  availableSites.forEach((site) => {
    if (site.category_model && site.category_model.slug) {
      const category = site.category_model;
      if (!categoriesMap.has(category.slug)) {
        categoriesMap.set(category.slug, {
          id: category.id,
          name: category.name,
          slug: category.slug,
          color: category.color,
        });
      }
    } else if (site.category) {
      // Fallback for old category format
      const categorySlug = site.category;
      if (!categoriesMap.has(categorySlug)) {
        categoriesMap.set(categorySlug, {
          id: categorySlug,
          name: categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1),
          slug: categorySlug,
        });
      }
    }
  });

  // Convert map to array
  availableCategories = Array.from(categoriesMap.values());

  // Sort categories by name
  availableCategories.sort((a, b) => a.name.localeCompare(b.name));

  renderFilterTabs();
}

function renderFilterTabs() {
  const filterTabsContainer = document.getElementById("filterTabs");
  if (!filterTabsContainer) return;

  // Clear existing tabs except "Semua"
  filterTabsContainer.innerHTML =
    '<button class="filter-tab active" data-category="all">Semua</button>';

  // Add category tabs
  availableCategories.forEach((category) => {
    const tab = document.createElement("button");
    tab.className = "filter-tab";
    tab.dataset.category = category.slug;
    tab.textContent = category.name;
    filterTabsContainer.appendChild(tab);
  });

  // Setup event listeners for new tabs
  setupFilterTabListeners();
}

function setupFilterTabListeners() {
  const tabs = document.querySelectorAll(".filter-tab");
  tabs.forEach((tab) => {
    // Remove existing listeners
    tab.replaceWith(tab.cloneNode(true));
  });

  // Add new listeners
  const newTabs = document.querySelectorAll(".filter-tab");
  newTabs.forEach((tab) => {
    tab.addEventListener("click", (e) => {
      // Remove active class from all tabs
      newTabs.forEach((t) => t.classList.remove("active"));
      // Add active class to clicked tab
      e.target.classList.add("active");
      // Filter sites
      const category = e.target.dataset.category;
      const searchTerm = searchInput ? searchInput.value : "";
      renderSites(category, searchTerm);
    });
  });
}

async function loadSites() {
  // Set timeout for auto-logout after 1 minute
  const timeoutId = setTimeout(() => {
    showFullPageLoading(false);
    showStatus(
      "Timeout: Proses memuat data terlalu lama. Logout otomatis...",
      "error"
    );
    setTimeout(() => {
      handleLogout();
    }, 2000);
  }, 60000); // 60 seconds

  // Ensure sites section is scrollable
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
  }

  try {
    const response = await fetch(`${API_BASE_URL}/sites`, {
      headers: {
        Authorization: `Bearer ${currentToken}`,
        Accept: "application/json",
      },
    });

    // Clear timeout if request succeeds
    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        availableSites = data.sites || [];
        importantInfoData = data.important_info || null;

        // Update loading message while rendering
        showFullPageLoading(
          true,
          "Menyiapkan daftar situs...",
          `Menampilkan ${availableSites.length} situs tersedia`
        );

        // Add small delay to show the message
        await new Promise((resolve) => setTimeout(resolve, 300));

        // Load categories and render sites
        await loadCategories();

        // Show important info if available
        if (importantInfoData) {
          console.log("Showing important info:", importantInfoData);
          importantInfo.show(importantInfoData);
        }

        // Pastikan sitesList ada sebelum merender
        if (!sitesList) {
          sitesList = document.getElementById("sitesList");
          if (!sitesList) {
            console.error(
              "Element sitesList tidak ditemukan saat akan merender"
            );
            // Coba buat elemen jika tidak ditemukan
            const sitesSection = document.getElementById("sitesSection");
            if (sitesSection) {
              sitesSection.innerHTML =
                '<div class="sites-grid" id="sitesList"></div>';
              sitesList = document.getElementById("sitesList");
            }
          }
        }

        // Log untuk debugging
        console.log("Sebelum render sites:", {
          availableSites: availableSites.length,
          sitesList: sitesList ? "ada" : "tidak ada",
        });

        // Render sites
        renderSites();

        // Hide loading after sites are rendered
        showFullPageLoading(false);

        showStatus(`${availableSites.length} situs berhasil dimuat`, "success");
      } else {
        showFullPageLoading(false);
        showStatus("Gagal memuat daftar situs", "error");
      }
    } else if (response.status === 403) {
      // Handle subscription expired
      const data = await response.json();
      if (data.subscription_expired) {
        showFullPageLoading(
          true,
          "Akun tidak aktif...",
          "Masa aktif akun telah berakhir"
        );
        await new Promise((resolve) => setTimeout(resolve, 2000));

        await clearStoredAuth();
        currentToken = null;
        currentUser = null;
        showFullPageLoading(false);
        showLoginScreen();
        showLoginStatus(
          "Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.",
          "error"
        );
        return;
      } else {
        showFullPageLoading(false);
        showStatus("Akses ditolak", "error");
      }
    } else {
      showFullPageLoading(false);
      showStatus("Gagal memuat daftar situs", "error");
    }
  } catch (error) {
    // Clear timeout if request fails
    clearTimeout(timeoutId);
    showFullPageLoading(false);
    console.error("Error loading sites:", error);
    showStatus("Error memuat situs", "error");
  }
}

function renderSites(filterCategory = "all", searchTerm = "") {
  // Pastikan sitesList ada
  if (!sitesList) {
    sitesList = document.getElementById("sitesList");
    if (!sitesList) {
      console.error("Element sitesList tidak ditemukan");
      return;
    }
  }

  // Kosongkan konten sitesList
  sitesList.innerHTML = "";

  // Log untuk debugging
  console.log("Rendering sites:", {
    availableSites: availableSites.length,
    filterCategory,
    searchTerm,
  });

  // Ensure sites section is scrollable
  const sitesSection = document.getElementById("sitesSection");
  if (sitesSection) {
    sitesSection.style.overflowY = "auto";
    sitesSection.style.height = "calc(100% - 120px)";
    sitesSection.style.maxHeight = "calc(100% - 120px)";
    sitesSection.style.flex = "1";

    // Ensure parent container has proper overflow settings
    const mainContent = document.querySelector(".main-content");
    if (mainContent) {
      mainContent.style.overflow = "hidden";
    }
  }

  // Tampilkan pesan jika tidak ada situs tersedia
  if (availableSites.length === 0) {
    sitesSection.innerHTML = `
            <div class="no-sites">
                <div class="no-sites-icon">🚪</div>
                <div class="no-sites-title">Tidak ada aplikasi tersedia</div>
                <div class="no-sites-subtitle">Silakan hubungi administrator untuk menambahkan aplikasi</div>
            </div>
        `;
    return;
  }

  // Reset sitesSection content jika sebelumnya menampilkan pesan "Tidak ada aplikasi tersedia"
  if (sitesSection.querySelector(".no-sites")) {
    sitesSection.innerHTML = '<div class="sites-grid" id="sitesList"></div>';
    sitesList = document.getElementById("sitesList");
  }

  // Pastikan sitesList ada dan kosong
  if (!sitesList) {
    sitesList = document.getElementById("sitesList");
    if (!sitesList) {
      console.error("Element sitesList tidak ditemukan");
      // Coba buat elemen jika tidak ditemukan
      sitesSection.innerHTML = '<div class="sites-grid" id="sitesList"></div>';
      sitesList = document.getElementById("sitesList");
      if (!sitesList) {
        console.error("Gagal membuat element sitesList");
        return;
      }
    }
  }

  let filteredSites = availableSites;

  // Filter by category
  if (filterCategory !== "all") {
    filteredSites = availableSites.filter((site) => {
      // Check if site has categoryModel relationship
      if (site.category_model && site.category_model.slug) {
        return site.category_model.slug === filterCategory;
      }
      // Fallback to old category field
      return site.category === filterCategory;
    });
  }

  // Filter by search term
  if (searchTerm.trim() !== "") {
    const searchLower = searchTerm.toLowerCase();
    filteredSites = filteredSites.filter((site) => {
      return (
        site.name.toLowerCase().includes(searchLower) ||
        site.domain.toLowerCase().includes(searchLower) ||
        (site.description &&
          site.description.toLowerCase().includes(searchLower))
      );
    });
  }

  if (filteredSites.length === 0) {
    const message =
      searchTerm.trim() !== ""
        ? `Tidak ada aplikasi yang ditemukan untuk "${searchTerm}"`
        : "Tidak ada aplikasi untuk kategori ini";
    sitesList.innerHTML = `
            <div class="no-sites">
                <div class="no-sites-icon">🔍</div>
                <div class="no-sites-title">${message}</div>
                <div class="no-sites-subtitle">Coba gunakan kata kunci yang berbeda atau pilih kategori lain</div>
            </div>
        `;
    return;
  }

  filteredSites.forEach((site) => {
    const siteCard = document.createElement("div");
    siteCard.className = "site-card";
    siteCard.dataset.category = site.category;

    // Generate logo based on site name
    const logoContent = getSiteLogo(site);

    // Get category info
    let categoryClass = site.category || "general";
    let categoryName = site.category_model
      ? site.category_model.name
      : categoryClass;

    // Generate description
    let description = site.description || `Akses ke ${site.domain}`;
    if (description.length > 50) {
      description = description.substring(0, 47) + "...";
    }

    // Get status based on category
    let statusClass = "general";
    if (site.category_model) {
      statusClass = site.category_model.slug;
    } else if (site.category) {
      statusClass = site.category;
    }

    siteCard.innerHTML = `
            <div class="site-logo">${logoContent}</div>
            <div class="site-info">
                <div class="site-name">${site.name}</div>
            </div>
        `;

    siteCard.addEventListener("click", () => openSiteWithCookies(site));
    sitesList.appendChild(siteCard);
  });
}

function getSiteLogo(site) {
  // Priority 1: Use logo_path from API v2 if available
  if (site.logo_path && site.logo_path.trim() !== "") {
    // Construct full URL if logo_path is a relative path
    const logoUrl = site.logo_path.startsWith("http")
      ? site.logo_path
      : `${API_BASE_URL.replace(
          "/api/v2",
          "/storage"
        )}/${site.logo_path.replace(/^\//, "")}`;

    return `<img src="${logoUrl}" alt="${site.name}">`;
  }

  // Priority 2: Use thumbnail from database if available (legacy support)
  if (site.thumbnail && site.thumbnail.trim() !== "") {
    // Construct full URL if thumbnail is a relative path
    const thumbnailUrl = site.thumbnail.startsWith("http")
      ? site.thumbnail
      : `${API_BASE_URL.replace(
          "/api/v2",
          "/storage"
        )}/${site.thumbnail.replace(/^\//, "")}`;

    return `<img src="${thumbnailUrl}" alt="${site.name}">`;
  }

  // Priority 3: Try to get favicon from URL or use fallback
  return getSiteLogoFallback(site.name, site.url);
}



              // Try Angular if available
              if (window.angular) {
                console.log(`🅰️ Triggering Angular events for ${fieldType}`);
                const scope = window.angular.element(field).scope();
                if (scope) {
                  scope.$apply();
                }
              }

              // Try Vue.js if available
              if (field.__vue__) {
                console.log(`🖖 Triggering Vue.js events for ${fieldType}`);
                field.__vue__.$forceUpdate();
              }

              // Force React re-render if possible
              if (field._reactInternalFiber || field._reactInternalInstance) {
                console.log(`⚛️ Triggering React events for ${fieldType}`);
                const reactEvent = new Event("input", { bubbles: true });
                Object.defineProperty(reactEvent, "target", {
                  value: field,
                  enumerable: true,
                });
                Object.defineProperty(reactEvent, "currentTarget", {
                  value: field,
                  enumerable: true,
                });
                field.dispatchEvent(reactEvent);
              }

              // Final event dispatch
              const finalEvents = [
                new Event("input", { bubbles: true }),
                new Event("change", { bubbles: true }),
                new Event("blur", { bubbles: true }),
                new CustomEvent("value-changed", {
                  detail: { value },
                  bubbles: true,
                }),
              ];

              finalEvents.forEach((event) => {
                try {
                  field.dispatchEvent(event);
                } catch (e) {
                  console.warn(
                    `⚠️ Failed to dispatch final ${event.type} event:`,
                    e
                  );
                }
              });

              setTimeout(() => {
                const success =
                  field.value === value ||
                  field.getAttribute("value") === value;
                if (success) {
                  console.log(
                    `✅ ${fieldType} filled successfully with DOM manipulation`
                  );
                } else {
                  console.error(
                    `❌ All methods failed for ${fieldType}. Field value: "${field.value}", Expected: "${value}"`
                  );
                }
                resolve(success);
              }, 200);
            } catch (error) {
              console.error(
                `❌ DOM manipulation failed for ${fieldType}:`,
                error
              );
              resolve(false);
            }
          });
        }

        // Enhanced field injection with retry mechanism
        async function performFieldInjection() {
          let emailSuccess = false;
          let passwordSuccess = false;

          // Fill email field with retry
          if (emailField && email) {
            console.log("📧 Starting email field injection...");
            for (let attempt = 1; attempt <= 3; attempt++) {
              console.log(`📧 Email injection attempt ${attempt}/3`);
              emailSuccess = await fillInputField(emailField, email, "Email");
              if (emailSuccess) break;

              if (attempt < 3) {
                console.log(
                  `⏳ Waiting before email retry attempt ${attempt + 1}...`
                );
                await new Promise((resolve) => setTimeout(resolve, 500));
              }
            }
          } else {
            console.warn("⚠️ Email field or value not found");
          }

          // Fill password field with retry
          if (passwordField && password) {
            console.log("🔒 Starting password field injection...");
            for (let attempt = 1; attempt <= 3; attempt++) {
              console.log(`🔒 Password injection attempt ${attempt}/3`);
              passwordSuccess = await fillInputField(
                passwordField,
                password,
                "Password"
              );
              if (passwordSuccess) break;

              if (attempt < 3) {
                console.log(
                  `⏳ Waiting before password retry attempt ${attempt + 1}...`
                );
                await new Promise((resolve) => setTimeout(resolve, 500));
              }
            }
          } else {
            console.warn("⚠️ Password field or value not found");
          }

          // Report injection results
          console.log(
            `📊 Injection Results - Email: ${
              emailSuccess ? "✅" : "❌"
            }, Password: ${passwordSuccess ? "✅" : "❌"}`
          );

          console.log("\n=== STATUS INJEKSI AKUN ===");
          console.log("📋 METODE: Account injection (BUKAN cookie injection)");
          console.log("📊 Hasil injeksi field:", {
            email_injection: emailSuccess ? "✅ Berhasil" : "❌ Gagal",
            password_injection: passwordSuccess ? "✅ Berhasil" : "❌ Gagal",
            ready_for_login: (emailSuccess || passwordSuccess) ? "✅ Ya" : "❌ Tidak"
          });

          // Attempt login even if some fields failed (partial success)
          if (emailSuccess || passwordSuccess) {
            await attemptLogin();
          } else {
            console.error(
              "❌ All field injections failed. Cannot proceed with login."
            );
            // Try one more time with different selectors
            console.log("🔄 Attempting emergency fallback injection...");
            await emergencyFallbackInjection();
          }
        }

        // Enhanced login attempt with multiple strategies
        async function attemptLogin() {
          if (!submitButton) {
            console.warn(
              "⚠️ Submit button not found, trying to find alternative login triggers"
            );
            await findAlternativeLoginTrigger();
            return;
          }

          console.log("🔘 Starting login attempt...");

          // Wait a moment for any field validations to complete
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Store initial URL to detect page changes
          const initialUrl = window.location.href;

          // Try multiple click methods for better compatibility
          for (let method = 1; method <= 5; method++) {
            console.log(`🔘 Login attempt method ${method}/5`);

            try {
              switch (method) {
                case 1:
                  // Method 1: Standard click
                  console.log("🖱️ Trying standard click...");
                  submitButton.focus();
                  submitButton.click();
                  console.log("✅ Submit button clicked (standard)");
                  break;

                case 2:
                  // Method 2: Focus and click with delay
                  console.log("🎯 Trying focus + click...");
                  submitButton.focus();
                  await new Promise((resolve) => setTimeout(resolve, 100));
                  submitButton.click();
                  console.log("✅ Submit button clicked (focus + click)");
                  break;

                case 3:
                  // Method 3: Mouse event simulation
                  console.log("🖱️ Trying mouse event simulation...");
                  const mouseEvents = ["mousedown", "mouseup", "click"];
                  mouseEvents.forEach((eventType) => {
                    const event = new MouseEvent(eventType, {
                      bubbles: true,
                      cancelable: true,
                      view: window,
                      button: 0,
                    });
                    submitButton.dispatchEvent(event);
                  });
                  console.log("✅ Submit button clicked (mouse simulation)");
                  break;

                case 4:
                  // Method 4: Keyboard simulation (Enter key)
                  console.log("⌨️ Trying keyboard simulation...");
                  submitButton.focus();
                  const enterEvent = new KeyboardEvent("keydown", {
                    key: "Enter",
                    code: "Enter",
                    bubbles: true,
                    cancelable: true,
                  });
                  submitButton.dispatchEvent(enterEvent);
                  console.log("✅ Submit button activated (Enter key)");
                  break;

                case 5:
                  // Method 5: Form submission
                  console.log("📝 Trying form submission...");
                  const form = submitButton.closest("form");
                  if (form) {
                    // Try to trigger form validation first
                    const submitEvent = new Event("submit", {
                      bubbles: true,
                      cancelable: true,
                    });
                    form.dispatchEvent(submitEvent);

                    // If that doesn't work, force submit
                    setTimeout(() => {
                      form.submit();
                    }, 100);
                    console.log("✅ Form submitted directly");
                  } else {
                    console.warn("⚠️ No form found for submission");
                    continue;
                  }
                  break;
              }

              // Wait to see if login was successful
              await new Promise((resolve) => setTimeout(resolve, 1500));

              // Check if we're still on the same page (login might have succeeded)
              if (window.location.href !== initialUrl) {
                console.log("\n=== INJEKSI AKUN BERHASIL ===");
                console.log("🎉 Login appears successful - page changed!");
                console.log("📋 KONFIRMASI: Login berhasil menggunakan INJEKSI AKUN (email + password)");
                console.log("📊 Hasil akhir:", {
                  method: "ACCOUNT_INJECTION",
                  status: "✅ BERHASIL",
                  url_changed: "✅ Ya",
                  login_successful: "✅ Ya"
                });

                // Execute post-submit script if provided
                if (config.jsAfterSubmit) {
                  console.log("🔧 Executing post-submit script");
                  try {
                    const postFunc = new Function(config.jsAfterSubmit);
                    postFunc();
                    console.log("✅ Post-submit script executed successfully");
                  } catch (error) {
                    console.error(
                      "❌ Error executing post-submit script:",
                      error
                    );
                  }
                }
                return;
              }
            } catch (error) {
              console.warn(`⚠️ Login method ${method} failed:`, error);
            }

            // Wait before trying next method
            if (method < 5) {
              await new Promise((resolve) => setTimeout(resolve, 500));
            }
          }

          console.log("\n=== INJEKSI AKUN GAGAL ===");
          console.error("❌ All login methods failed");
          console.log("📋 KONFIRMASI: Login gagal menggunakan INJEKSI AKUN (email + password)");
          console.log("📊 Hasil akhir:", {
            method: "ACCOUNT_INJECTION",
            status: "❌ GAGAL",
            url_changed: "❌ Tidak",
            login_successful: "❌ Tidak",
            reason: "Semua metode login gagal"
          });

          // Execute post-submit script anyway (might be needed for some sites)
          if (config.jsAfterSubmit) {
            console.log("🔧 Executing post-submit script as fallback");
            try {
              const postFunc = new Function(config.jsAfterSubmit);
              postFunc();
              console.log("✅ Post-submit script executed successfully");
            } catch (error) {
              console.error("❌ Error executing post-submit script:", error);
            }
          }
        }

        // Emergency fallback injection for extremely stubborn sites
        async function emergencyFallbackInjection() {
          console.log("🚨 Emergency fallback injection activated");

          // Try to find any input fields and buttons on the page
          const allInputs = document.querySelectorAll(
            'input[type="email"], input[type="text"], input[name*="email"], input[id*="email"], input[placeholder*="email"], input[placeholder*="Email"]'
          );
          const allPasswordInputs = document.querySelectorAll(
            'input[type="password"], input[name*="password"], input[id*="password"], input[placeholder*="password"], input[placeholder*="Password"]'
          );
          const allButtons = document.querySelectorAll(
            'button, input[type="submit"], input[type="button"], [role="button"]'
          );

          console.log(
            `🔍 Found ${allInputs.length} potential email fields, ${allPasswordInputs.length} password fields, ${allButtons.length} buttons`
          );

          // Try to fill the first available email field
          if (allInputs.length > 0 && email) {
            for (const input of allInputs) {
              if (input.offsetParent !== null) {
                // Check if visible
                console.log("🚨 Attempting emergency email injection...");
                const success = await fillInputField(
                  input,
                  email,
                  "emergency-email"
                );
                if (success) break;
              }
            }
          }

          // Try to fill the first available password field
          if (allPasswordInputs.length > 0 && password) {
            for (const input of allPasswordInputs) {
              if (input.offsetParent !== null) {
                // Check if visible
                console.log("🚨 Attempting emergency password injection...");
                const success = await fillInputField(
                  input,
                  password,
                  "emergency-password"
                );
                if (success) break;
              }
            }
          }

          // Try to click any button that looks like a login button
          if (allButtons.length > 0) {
            for (const button of allButtons) {
              const buttonText = (
                button.textContent ||
                button.value ||
                button.getAttribute("aria-label") ||
                ""
              ).toLowerCase();
              if (
                buttonText.includes("login") ||
                buttonText.includes("sign in") ||
                buttonText.includes("masuk") ||
                buttonText.includes("submit")
              ) {
                if (button.offsetParent !== null) {
                  // Check if visible
                  console.log("🚨 Attempting emergency button click...");
                  try {
                    button.click();
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                    break;
                  } catch (error) {
                    console.warn("⚠️ Emergency button click failed:", error);
                  }
                }
              }
            }
          }
        }

        // Find alternative login triggers when submit button is not found
        async function findAlternativeLoginTrigger() {
          console.log("🔍 Searching for alternative login triggers...");

          // Look for buttons with login-related text
          const loginTexts = [
            "login",
            "sign in",
            "log in",
            "masuk",
            "submit",
            "continue",
            "next",
            "lanjut",
          ];
          const allButtons = document.querySelectorAll(
            'button, input[type="submit"], input[type="button"], [role="button"]'
          );

          for (const text of loginTexts) {
            for (const button of allButtons) {
              const buttonText = (
                button.textContent ||
                button.value ||
                button.getAttribute("aria-label") ||
                ""
              ).toLowerCase();
              if (
                buttonText.includes(text.toLowerCase()) &&
                button.offsetParent !== null
              ) {
                console.log(
                  `🎯 Found alternative login trigger: "${buttonText}"`
                );
                try {
                  button.click();
                  await new Promise((resolve) => setTimeout(resolve, 1000));
                  return;
                } catch (error) {
                  console.warn("⚠️ Alternative trigger click failed:", error);
                }
              }
            }
          }

          console.warn("⚠️ No alternative login triggers found");
        }

        // Start the enhanced injection process
        await performFieldInjection();
      } catch (error) {
        console.error("❌ Error during field injection:", error);
      }
    }

    // Start waiting for elements
    setTimeout(async () => await waitForElements(), 1000);
  } catch (error) {
    console.error("❌ Error during account injection:", error);
  }
}

function getSiteLogoFallback(siteName, siteUrl) {
  const domain = new URL(siteUrl).hostname;
  const firstLetter = siteName.charAt(0).toUpperCase();

  // Common site logos mapping with better emojis
  const logoMap = {
    "youtube.com": "📺",
    "netflix.com": "🎬",
    "spotify.com": "🎵",
    "github.com": "💻",
    "google.com": "🔍",
    "facebook.com": "📘",
    "twitter.com": "🐦",
    "instagram.com": "📷",
    "linkedin.com": "💼",
    "amazon.com": "🛒",
    "apple.com": "🍎",
    "microsoft.com": "🪟",
    "discord.com": "💬",
    "reddit.com": "🤖",
    "twitch.tv": "🎮",
    "tiktok.com": "🎭",
    "whatsapp.com": "💬",
    "telegram.org": "✈️",
    "zoom.us": "📹",
    "dropbox.com": "📦",
    "canva.com": "🎨",
    "figma.com": "🎨",
    "notion.so": "📝",
    "slack.com": "💼",
  };

  // Check if we have a custom emoji for this domain
  for (const [siteDomain, emoji] of Object.entries(logoMap)) {
    if (domain.includes(siteDomain)) {
      return `<div style="font-size: 32px; font-weight: bold;">${emoji}</div>`;
    }
  }

  // Try to load favicon with better fallback
  const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=128`;
  return `<img src="${faviconUrl}" alt="${siteName}" onerror="this.onerror=null; this.parentElement.innerHTML='<div style=\"font-size: 28px; font-weight: bold; color: rgba(255,255,255,0.9); text-shadow: 0 2px 4px rgba(0,0,0,0.3);\">${firstLetter}</div>';">`;
}

async function openSiteWithCookies(site) {
  try {
    console.log("🚀 MANUAL COOKIE IMPORT - Opening site with cookies:", site);
    console.log(
      "🔄 Auto-inject disabled - importing cookies only on manual click"
    );

    // Validate site data
    if (!site || !site.id || !site.url) {
      throw new Error("Invalid site data");
    }

    // Show initial loading
    showFullPageLoading(
      true,
      `Menyiapkan ${site.name}...`,
      "Menyiapkan aplikasi"
    );
    showStatus(`Memuat data untuk ${site.name}...`, "success");

    // Check for custom redirect first (this doesn't depend on JSON files)
    if (site.enable_custom_redirect && site.redirect_url) {
      console.log("🔄 Custom redirect detected for site:", site.name);

      const redirectDelay = site.redirect_delay || 5;
      let countdown = redirectDelay;

      showFullPageLoading(
        true,
        site.redirect_title || `Menyiapkan ${site.name}...`,
        site.redirect_content || `Mengalihkan dalam ${countdown} detik`
      );
      showStatus(`Mengalihkan ke ${site.name}...`, "success");

      // Start countdown
      const countdownInterval = setInterval(() => {
        countdown--;
        if (countdown > 0) {
          showFullPageLoading(
            true,
            site.redirect_title || `Menyiapkan ${site.name}...`,
            site.redirect_content || `Mengalihkan dalam ${countdown} detik`
          );
        } else {
          clearInterval(countdownInterval);
          showFullPageLoading(
            true,
            "Menyiapkan URL...",
            "Membuka halaman tujuan"
          );

          // Open the redirect URL
          setTimeout(async () => {
            await chrome.tabs.create({
              url: site.redirect_url,
              active: true,
            });
            showFullPageLoading(false);
            showStatus(`Berhasil mengalihkan ke ${site.name}`, "success");
          }, 500);
        }
      }, 1000);

      return;
    }

    let cookiesData = null;

    // First try to get cookies from cache via background script
    try {
      const cachedCookiesResponse = await new Promise((resolve) => {
        chrome.runtime.sendMessage(
          {
            action: "getCachedCookiesForSite",
            siteId: site.id,
          },
          resolve
        );
      });

      if (
        cachedCookiesResponse &&
        cachedCookiesResponse.success &&
        cachedCookiesResponse.data
      ) {
        console.log(
          `Using cached cookies for site: ${site.name} (${cachedCookiesResponse.data.cookies.length} cookies)`
        );
        cookiesData = {
          success: true,
          data: {
            cookies: cachedCookiesResponse.data.cookies,
            site: {
              id: cachedCookiesResponse.data.siteId,
              name: cachedCookiesResponse.data.siteName,
              domain: cachedCookiesResponse.data.domain,
            },
          },
        };
      }
    } catch (error) {
      console.warn("Error getting cached cookies:", error);
    }

    // Fallback to API if not in cache
    if (!cookiesData) {
      console.log("Fetching site details from API for site:", site.name);
      showFullPageLoading(
        true,
        `Mengambil data ${site.name}...`,
        "Menyiapkan aplikasi"
      );

      try {
        const response = await fetch(
          `${API_BASE_URL}/site-details/${site.id}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${currentToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const siteDetailData = await response.json();
          console.log("Fetched site details from API:", siteDetailData);

          // Transform the data to match expected format
          if (siteDetailData.success && siteDetailData.data.json_files) {
            cookiesData = {
              success: true,
              data: {
                site: siteDetailData.data.site,
                json_files: siteDetailData.data.json_files,
              },
            };
          } else {
            cookiesData = { success: true, data: { json_files: [] } };
          }
        } else {
          console.warn(
            "Failed to fetch site details from API, proceeding without cookies"
          );
          cookiesData = { success: true, data: { json_files: [] } };
        }
      } catch (apiError) {
        console.warn("API error, proceeding without cookies:", apiError);
        cookiesData = { success: true, data: { json_files: [] } };
      }
    }

    if (!cookiesData || !cookiesData.success) {
      console.warn(
        "Invalid site details response, proceeding without JSON files"
      );
      cookiesData = { success: true, data: { json_files: [] } };
    }

    // Combine site info with JSON files data
    const siteData = {
      ...site,
      json_files: cookiesData.data ? cookiesData.data.json_files || [] : [],
    };

    // Update site info if available from API
    if (cookiesData.data && cookiesData.data.site) {
      Object.assign(siteData, {
        name: cookiesData.data.site.name || siteData.name,
        url: cookiesData.data.site.url || siteData.url,
        description: cookiesData.data.site.description || siteData.description,
        logo_path: cookiesData.data.site.logo_path || siteData.logo_path,
      });
    }

    console.log("Site data with JSON files:", siteData);

    // Prepare JSON files with index for display
    const jsonFilesWithIndex = [];
    if (siteData.json_files && Array.isArray(siteData.json_files)) {
      siteData.json_files.forEach((jsonFile, index) => {
        jsonFilesWithIndex.push({
          ...jsonFile,
          index: index + 1,
          display_name: jsonFile.name || `File JSON #${index + 1}`,
        });
      });
    }

    // If there are multiple JSON files, show the site detail page
    if (jsonFilesWithIndex.length > 1) {
      console.log(
        `📁 Site has multiple JSON files (${jsonFilesWithIndex.length}), showing selection screen`
      );
      showFullPageLoading(false);

      // Show site detail with JSON files
      siteDetail.show(
        siteData,
        jsonFilesWithIndex,
        () => {
          // On close callback
          console.log("Site detail closed");
        },
        async (site, selectedJsonFile) => {
          // On JSON file select callback
          console.log(`Selected JSON file: ${selectedJsonFile.display_name}`);

          // Fetch cookies from the selected JSON file
          try {
            showFullPageLoading(
              true,
              `Memuat ${site.name} ${selectedJsonFile.index}`,
              "Menyiapkan data"
            );

            const response = await fetch(
              `${API_BASE_URL}/json-files/${selectedJsonFile.id}/cookies`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${currentToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (response.ok) {
              const jsonFileData = await response.json();
              if (jsonFileData.success && jsonFileData.data.cookies) {
                // Update loading message with cookie count
                showFullPageLoading(
                  true,
                  `Memuat ${site.name} ${selectedJsonFile.index}`,
                  `Menyiapkan ${jsonFileData.data.cookies.length} Data`
                );

                // Create a new siteData with cookies from selected JSON file
                const siteWithSelectedCookies = {
                  ...site,
                  cookies: jsonFileData.data.cookies,
                  selectedJsonFile: selectedJsonFile,
                };
                await injectCookieAndOpenSite(siteWithSelectedCookies);
              } else {
                showStatus(
                  `File JSON ${selectedJsonFile.display_name} tidak memiliki cookie yang valid`,
                  "warning"
                );
                showFullPageLoading(false);
              }
            } else {
              showStatus(
                `Gagal memuat cookie dari ${selectedJsonFile.display_name}`,
                "error"
              );
              showFullPageLoading(false);
            }
          } catch (error) {
            console.error("Error loading JSON file cookies:", error);
            showStatus(`Error: ${error.message}`, "error");
            showFullPageLoading(false);
          }
        }
      );
      return;
    }

    // Check for custom redirect after JSON files are loaded
    if (
      siteData.redirect_url &&
      siteData.redirect_title &&
      siteData.redirect_content
    ) {
      console.log("🔄 Custom redirect detected for site:", siteData.name);

      // Create redirect page
      const redirectTab = await chrome.tabs.create({
        url: chrome.runtime.getURL("redirect.html"),
        active: true,
      });

      // Send redirect data to the page
      setTimeout(() => {
        chrome.tabs.sendMessage(redirectTab.id, {
          type: "SETUP_REDIRECT",
          data: {
            title: siteData.redirect_title,
            content: siteData.redirect_content,
            targetUrl: siteData.redirect_url,
            siteName: siteData.name,
          },
        });
      }, 500);

      showStatus(`Mengalihkan ke ${siteData.name}`, "success");
      showFullPageLoading(false);
      return;
    }

    // If there's only one JSON file, load its cookies directly
    if (jsonFilesWithIndex.length === 1) {
      console.log(`📁 Site has one JSON file, loading cookies directly`);
      showFullPageLoading(
        true,
        `Memuat ${siteData.name} ${jsonFilesWithIndex[0].index}`,
        "Menyiapkan data"
      );

      try {
        const response = await fetch(
          `${API_BASE_URL}/json-files/${jsonFilesWithIndex[0].id}/cookies`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${currentToken}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const jsonFileData = await response.json();
          if (jsonFileData.success && jsonFileData.data.cookies) {
            // Update loading message with cookie count
            showFullPageLoading(
              true,
              `Memuat ${siteData.name}`,
              `Menyiapkan ${jsonFileData.data.cookies.length} Data`
            );

            siteData.cookies = jsonFileData.data.cookies;
            siteData.selectedJsonFile = jsonFilesWithIndex[0];
          } else {
            console.warn("JSON file has no valid cookies");
            siteData.cookies = [];
          }
        } else {
          console.warn("Failed to load cookies from JSON file");
          siteData.cookies = [];
        }
      } catch (error) {
        console.error("Error loading JSON file cookies:", error);
        siteData.cookies = [];
      }
    } else if (jsonFilesWithIndex.length === 0) {
      console.log(`📁 Site has no JSON files`);
      siteData.cookies = [];

      // Check for custom redirect first (when no JSON files)
      if (siteData.enable_custom_redirect && siteData.redirect_url) {
        console.log(
          "🔄 Custom redirect detected for site (no JSON files):",
          siteData.name
        );
        showFullPageLoading(
          true,
          `Mengalihkan ke ${siteData.name}...`,
          "Menyiapkan pengalihan kustom"
        );

        // Create custom redirect page
        const redirectTab = await chrome.tabs.create({
          url: chrome.runtime.getURL("redirect.html"),
          active: true,
        });

        // Send redirect data to the new tab
        setTimeout(() => {
          chrome.tabs.sendMessage(redirectTab.id, {
            action: "setupCustomRedirect",
            data: {
              title: siteData.redirect_title || siteData.name,
              content:
                siteData.redirect_content ||
                `Mengalihkan ke ${siteData.name}...`,
              redirectUrl: siteData.redirect_url,
              delay: siteData.redirect_delay || 3000,
            },
          });
        }, 1000);

        showFullPageLoading(false);
        showStatus(`Berhasil mengalihkan ke ${siteData.name}`, "success");
        return;
      }

      // Check for account injection (sites without JSON files but with login injection)
      if (
        siteData.login_url &&
        siteData.email_selector &&
        siteData.password_selector &&
        siteData.submit_selector
      ) {
        try {
          console.log("\n=== METODE INJEKSI: ACCOUNT INJECTION ===");
          console.log("🔑 Account injection detected for site:", siteData.name);
          console.log("📋 Alasan: Tidak ada JSON files, menggunakan injeksi akun");
          console.log("📊 Status JSON files:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "ACCOUNT_INJECTION"
          });
          console.log("📋 Injection config:", {
            login_url: siteData.login_url,
            email_selector: siteData.email_selector,
            password_selector: siteData.password_selector,
            submit_selector: siteData.submit_selector,
            has_additional_script: !!siteData.additional_script,
            has_js_after_submit: !!siteData.js_after_submit,
          });
          showFullPageLoading(
            true,
            `Melakukan injeksi akun ${siteData.name}...`,
            "Menyiapkan login otomatis"
          );

          // Open login URL and inject account
          const loginTab = await chrome.tabs.create({
            url: siteData.login_url,
            active: true,
          });

          // Get account credentials from API first
          let accountCredentials = null;

          try {
            const accountResponse = await fetch(
              `${API_BASE_URL}/site-accounts/${siteData.id}`,
              {
                method: "GET",
                headers: {
                  Authorization: `Bearer ${currentToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (accountResponse.ok) {
              const accountData = await accountResponse.json();

              if (
                accountData.success &&
                accountData.data &&
                accountData.data.accounts &&
                accountData.data.accounts.length > 0
              ) {
                // Use the first available alternative account
                const firstAccount = accountData.data.accounts[0];

                if (firstAccount.email && firstAccount.password) {
                  accountCredentials = {
                    email: firstAccount.email,
                    password: firstAccount.password,
                  };
                  console.log("🔑 Using alternative account credentials");
                }
              }
            }
          } catch (error) {
            console.warn(
              "Error getting alternative account credentials:",
              error
            );
          }

          // Fallback to main site account if no alternative accounts
          if (!accountCredentials) {
            if (siteData.email && siteData.password) {
              accountCredentials = {
                email: siteData.email,
                password: siteData.password,
              };
              console.log("🔑 Using main site account credentials");
            } else {
              throw new Error(
                "No account credentials available (neither alternative nor main account)"
              );
            }
          }

          // Wait for page to load then inject login script
          setTimeout(async () => {
            try {
              await chrome.scripting.executeScript({
                target: { tabId: loginTab.id },
                func: injectAccountLogin,
                args: [
                  {
                    emailSelector: siteData.email_selector,
                    passwordSelector: siteData.password_selector,
                    submitSelector: siteData.submit_selector,
                    additionalScript: siteData.additional_script,
                    jsAfterSubmit: siteData.js_after_submit,
                    siteName: siteData.name,
                    email: accountCredentials.email,
                    password: accountCredentials.password,
                  },
                ],
              });

              showStatus(
                `Berhasil melakukan injeksi akun untuk ${siteData.name}`,
                "success"
              );
            } catch (error) {
              console.error("Error injecting account login:", error);
              showStatus(
                `Gagal melakukan injeksi akun untuk ${siteData.name}`,
                "error"
              );
            }
          }, 2000);
        } catch (error) {
          console.error("Error during account injection setup:", error);
          showStatus(
            `Gagal menyiapkan injeksi akun untuk ${siteData.name}: ${error.message}`,
            "error"
          );
          showFullPageLoading(false);
          return;
        }

        showFullPageLoading(false);
        return;
      } else {
        // Check if partial injection data exists
        const hasPartialInjection =
          siteData.login_url ||
          siteData.email_selector ||
          siteData.password_selector ||
          siteData.submit_selector;

        console.log("\n=== METODE INJEKSI: DIRECT SITE ACCESS ===");
        if (hasPartialInjection) {
          console.log(
            "⚠️ Partial injection data detected for site:",
            siteData.name
          );
          console.log("📋 Alasan: Konfigurasi injeksi akun tidak lengkap");
          console.log("📊 Status injeksi:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "DIRECT_ACCESS_PARTIAL_CONFIG",
            available_fields: {
              login_url: !!siteData.login_url,
              email_selector: !!siteData.email_selector,
              password_selector: !!siteData.password_selector,
              submit_selector: !!siteData.submit_selector,
            }
          });
          showStatus(
            `${siteData.name} memiliki konfigurasi injeksi yang tidak lengkap. Membuka situs langsung.`,
            "warning"
          );
        } else {
          console.log(
            "📄 No JSON files or injection data, opening site directly:",
            siteData.name
          );
          console.log("📋 Alasan: Tidak ada JSON files dan tidak ada konfigurasi injeksi akun");
          console.log("📊 Status injeksi:", {
            json_files_count: jsonFilesWithIndex.length,
            has_cookies: false,
            injection_method: "DIRECT_ACCESS_NO_CONFIG"
          });
          showStatus(`Membuka ${siteData.name}`, "success");
        }

        chrome.tabs.create({ url: siteData.url, active: true });
        showFullPageLoading(false);
        return;
      }
    }

    // Proceed with cookie injection if we have cookies
    if (
      siteData.cookies &&
      Array.isArray(siteData.cookies) &&
      siteData.cookies.length > 0
    ) {
      console.log("\n=== METODE INJEKSI: COOKIE INJECTION ===");
      console.log("🍪 Cookie injection detected for site:", siteData.name);
      console.log("📋 Alasan: JSON files tersedia dengan cookies");
      console.log("📊 Status cookies:", {
        cookies_count: siteData.cookies.length,
        has_json_files: jsonFilesWithIndex.length > 0,
        injection_method: "COOKIE_INJECTION"
      });

      // Validate and clean cookies data
      siteData.cookies = siteData.cookies.filter((cookie) => {
        // Basic validation
        if (!cookie.name || cookie.value === undefined) {
          console.warn(
            "Skipping invalid cookie (missing name or value):",
            cookie
          );
          return false;
        }

        // Ensure domain is set
        if (!cookie.domain && siteData.domain) {
          cookie.domain = siteData.domain;
          console.log(`Set domain for cookie ${cookie.name}: ${cookie.domain}`);
        }

        return true;
      });
      console.log(
        `🍪 Starting direct cookie overwrite for ${siteData.name} (${siteData.cookies.length} cookies)`
      );
      showFullPageLoading(
        true,
        `Menyiapkan aplikasi...`,
        `Memproses ${siteData.cookies.length} data untuk ${siteData.name}`
      );
      showStatus(
        `Memproses ${siteData.cookies.length} data untuk ${siteData.name}...`,
        "success"
      );

      // DISABLED: Cookie clearing to prevent looping
      // Using direct overwrite strategy instead
      console.log("🔄 Using direct overwrite strategy - no clearing needed");
      console.log("📋 Cookies to set:", siteData.cookies);

      // Set cookies
      let successCount = 0;
      let errorCount = 0;

      // Try to use background script for better performance
      try {
        console.log(
          "📤 Sending cookies to background script for bulk setting..."
        );
        const response = await chrome.runtime.sendMessage({
          action: "setCookies",
          cookies: siteData.cookies,
          domain: siteData.domain,
        });

        if (response && response.success) {
          successCount = response.result.success;
          errorCount = response.result.failed;
          console.log("✅ Bulk cookie setting result:", response.result);
        } else {
          throw new Error(response?.error || "Background script failed");
        }
      } catch (error) {
        console.warn(
          "⚠️ Background script failed, falling back to individual setting:",
          error
        );

        // Fallback to individual cookie setting
        console.log(
          "🔄 Using fallback method - setting cookies individually..."
        );
        for (let i = 0; i < siteData.cookies.length; i++) {
          const cookie = siteData.cookies[i];
          console.log(
            `🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`,
            cookie
          );

          try {
            await setCookieWithExtensionAPI(cookie, siteData.domain);
            successCount++;
            console.log(`✅ Cookie ${cookie.name} set successfully`);
          } catch (error) {
            errorCount++;
            console.error("❌ Cookie set error:", error);
            console.warn("📋 Cookie data:", cookie);
          }
        }
      }

      // Show detailed results
      if (successCount > 0) {
        const statusMessage = `${successCount}/${
          siteData.cookies.length
        } data berhasil diproses${
          errorCount > 0 ? ` (${errorCount} gagal)` : ""
        }. Membuka ${siteData.name}...`;
        showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
        console.log(
          `🎯 Manual cookie import completed: ${successCount} success, ${errorCount} failed`
        );
      } else {
        showStatus(
          `Gagal memproses semua data. Membuka ${siteData.name} tanpa data tambahan...`,
          "error"
        );
        console.error("❌ All cookies failed to set");
      }

      // Add small delay before opening site to ensure cookies are properly set
      console.log(
        "⏳ Waiting 300ms before opening site to ensure cookies are set..."
      );
      await new Promise((resolve) => setTimeout(resolve, 300));
    } else {
      showStatus(`Membuka ${siteData.name}...`, "success");
      console.log("ℹ️ No cookies to set for this site - opening directly");
    }

    // Open site in new tab
    showFullPageLoading(
      true,
      `Membuka ${siteData.name}...`,
      "Menyiapkan tab baru"
    );

    try {
      console.log(`🌐 Opening site in new tab: ${siteData.url}`);
      chrome.tabs.create({ url: siteData.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(siteData.url, "_blank");
    }

    // Call the function to inject cookies and open site
    await injectCookieAndOpenSite(siteData);
  } catch (error) {
    console.error("❌ Manual cookie import error:", error);

    // Hide loading on error
    showFullPageLoading(false);

    if (
      error.message &&
      error.message.includes("Extension context invalidated")
    ) {
      console.error("🔄 Extension context invalidated - reload required");
      showStatus("Ekstensi perlu di-reload untuk membuka situs.", "error");
      showTroubleshooting();
    } else {
      console.error(`💥 Unexpected error: ${error.message}`);
      showStatus(`Error: ${error.message}`, "error");
    }
  }
}

/**
 * Inject cookies and open site
 * @param {Object} siteData - Site data with cookies
 */
async function injectCookieAndOpenSite(siteData) {
  try {
    // Set cookies
    let successCount = 0;
    let errorCount = 0;

    // Try to use background script for better performance
    try {
      console.log(
        "📤 Sending cookies to background script for bulk setting..."
      );
      const response = await chrome.runtime.sendMessage({
        action: "setCookies",
        cookies: siteData.cookies,
        domain: siteData.domain,
      });

      if (response && response.success) {
        successCount = response.result.success;
        errorCount = response.result.failed;
        console.log("✅ Bulk cookie setting result:", response.result);
      } else {
        throw new Error(response?.error || "Background script failed");
      }
    } catch (error) {
      console.warn(
        "⚠️ Background script failed, falling back to individual setting:",
        error
      );

      // Fallback to individual cookie setting
      console.log("🔄 Using fallback method - setting cookies individually...");
      for (let i = 0; i < siteData.cookies.length; i++) {
        const cookie = siteData.cookies[i];
        console.log(
          `🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`,
          cookie
        );

        try {
          await setCookieWithExtensionAPI(cookie, siteData.domain);
          successCount++;
          console.log(`✅ Cookie ${cookie.name} set successfully`);
        } catch (error) {
          errorCount++;
          console.error("❌ Cookie set error:", error);
          console.warn("📋 Cookie data:", cookie);
        }
      }
    }

    // Show detailed results
    if (successCount > 0) {
      const statusMessage = `${successCount}/${
        siteData.cookies.length
      } data berhasil diproses${
        errorCount > 0 ? ` (${errorCount} gagal)` : ""
      }. Membuka ${siteData.name}...`;
      showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
      console.log(
        `🎯 Manual cookie import completed: ${successCount} success, ${errorCount} failed`
      );
    } else {
      showStatus(
        `Gagal memproses semua data. Membuka ${siteData.name} tanpa data tambahan...`,
        "error"
      );
      console.error("❌ All cookies failed to set");
    }

    // Add small delay before opening site to ensure cookies are properly set
    console.log(
      "⏳ Waiting 300ms before opening site to ensure cookies are set..."
    );
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Open site in new tab
    showFullPageLoading(
      true,
      `Membuka ${siteData.name}...`,
      "Menyiapkan tab baru"
    );

    try {
      console.log(`🌐 Opening site in new tab: ${siteData.url}`);
      chrome.tabs.create({ url: siteData.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(siteData.url, "_blank");
    }

    // Hide loading
    showFullPageLoading(false);
  } catch (error) {
    console.error("❌ Error injecting cookies and opening site:", error);
    showFullPageLoading(false);
    showStatus(`Error: ${error.message}`, "error");
  }
}

async function setCookieWithExtensionAPI(cookie, siteDomain) {
  return new Promise((resolve, reject) => {
    // Validate required fields
    if (!cookie.name || cookie.value === undefined) {
      reject(new Error("Cookie name and value are required"));
      return;
    }

    // Clean siteDomain (remove protocol if present)
    const cleanSiteDomain = siteDomain
      .replace(/^https?:\/\//, "")
      .replace(/\/.*$/, "");

    // Determine protocol based on secure flag
    const protocol = cookie.secure !== false ? "https" : "http";

    // Prepare cookie for Chrome API
    const cookieDetails = {
      url: `${protocol}://${cleanSiteDomain}`,
      name: cookie.name,
      value: cookie.value,
    };

    console.log(`Using site domain: ${cleanSiteDomain}, protocol: ${protocol}`);

    // Handle domain - prioritize cookie.domain, fallback to siteDomain
    if (cookie.domain) {
      // Clean domain (remove protocol if present)
      let cleanDomain = cookie.domain
        .replace(/^https?:\/\//, "")
        .replace(/\/.*$/, "");

      // If domain doesn't start with dot and is not the exact siteDomain, add dot for subdomain support
      if (!cleanDomain.startsWith(".") && cleanDomain !== cleanSiteDomain) {
        cookieDetails.domain = `.${cleanDomain}`;
      } else {
        cookieDetails.domain = cleanDomain;
      }
    } else {
      cookieDetails.domain = cleanSiteDomain;
    }

    console.log(
      `Domain mapping: ${cookie.domain || "undefined"} -> ${
        cookieDetails.domain
      }`
    );

    // Handle path
    cookieDetails.path = cookie.path || "/";

    // Handle secure flag
    if (cookie.secure !== undefined) {
      cookieDetails.secure = cookie.secure;
    } else {
      // Default to true for HTTPS sites
      cookieDetails.secure = true;
    }

    // Handle httpOnly flag
    if (cookie.httpOnly !== undefined) {
      cookieDetails.httpOnly = cookie.httpOnly;
    }

    // Handle sameSite
    if (cookie.sameSite) {
      const validSameSite = ["unspecified", "no_restriction", "lax", "strict"];
      if (validSameSite.includes(cookie.sameSite)) {
        cookieDetails.sameSite = cookie.sameSite;
      } else if (cookie.sameSite === "none") {
        cookieDetails.sameSite = "no_restriction";
      } else {
        // Default mapping for common values
        cookieDetails.sameSite = "lax";
      }
    }

    // Handle expiration date
    if (cookie.expirationDate) {
      // Handle both Unix timestamp and Date object
      if (typeof cookie.expirationDate === "number") {
        cookieDetails.expirationDate = cookie.expirationDate;
      } else if (cookie.expirationDate instanceof Date) {
        cookieDetails.expirationDate = Math.floor(
          cookie.expirationDate.getTime() / 1000
        );
      }
    } else {
      // Set expiration to 1 year from now if not specified
      cookieDetails.expirationDate =
        Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60;
    }

    console.log("Setting cookie:", cookieDetails);

    // Function to attempt setting cookie
    const attemptSetCookie = (details, isRetry = false) => {
      try {
        chrome.cookies.set(details, (result) => {
          if (chrome.runtime.lastError) {
            console.warn("Cookie set error:", chrome.runtime.lastError.message);
            console.warn("Cookie details:", details);

            // If HTTPS failed and this is not a retry, try with HTTP
            if (!isRetry && details.url.startsWith("https://")) {
              console.log("Retrying with HTTP...");
              const httpDetails = { ...details };
              httpDetails.url = details.url.replace("https://", "http://");
              httpDetails.secure = false;
              attemptSetCookie(httpDetails, true);
            } else {
              reject(new Error(chrome.runtime.lastError.message));
            }
          } else {
            console.log("Cookie set successfully:", result);
            resolve(result);
          }
        });
      } catch (error) {
        console.warn("Chrome cookies API error:", error);
        reject(new Error("Extension context invalidated"));
      }
    };

    // Start the attempt
    attemptSetCookie(cookieDetails);
  });
}

function showLoginScreen() {
  // Ensure loading is hidden when showing login screen
  showFullPageLoading(false);

  // Reset opacity untuk animasi berikutnya
  loginScreen.style.opacity = "0";
  loginScreen.style.transition = "opacity 0.5s ease-in";

  // Tampilkan login screen dengan animasi
  loginScreen.style.display = "block";

  // Trigger reflow untuk memastikan transisi berjalan
  loginScreen.offsetHeight;

  // Mulai animasi fade-in
  loginScreen.style.opacity = "1";

  // Sembunyikan main screen
  mainScreen.style.display = "none";
}

function showMainScreen() {
  // Pastikan main screen ditampilkan dengan display flex
  mainScreen.style.display = "flex";

  // Hanya ubah display loginScreen jika opasitasnya bukan 0
  // (jika tidak sedang dalam animasi fade-out)
  if (loginScreen.style.opacity !== "0") {
    loginScreen.style.display = "none";
  }

  // Update user info
  userName.textContent = currentUser.name;
  userRole.textContent = currentUser.role;
  userRole.className = `role-badge ${currentUser.role}`;

  // Update app version
  updateAppVersion();

  // Load important info for scrolling text
  loadImportantInfo();
}

function showLoginStatus(message, type) {
  loginStatus.textContent = message;
  loginStatus.className = `status ${type}`;
  loginStatus.style.display = "block";

  setTimeout(() => {
    loginStatus.style.display = "none";
  }, 3000);
}

function showStatus(message, type) {
  if (statusDiv) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = "block";

    setTimeout(() => {
      statusDiv.style.display = "none";
    }, 3000);
  }
}

function showTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "block";
  }
}

function hideTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "none";
  }
}

// Update UI based on authentication status
function updateUI() {
  if (currentUser && currentToken) {
    // Show main screen
    if (loginScreen) loginScreen.style.display = "none";
    if (mainScreen) mainScreen.style.display = "block";

    // Update user info
    if (userName) userName.textContent = currentUser.name;
    if (userRole) {
      userRole.textContent =
        currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
      userRole.className = `role-badge ${currentUser.role}`;
    }

    // Update subscription info
    updateSubscriptionInfo();

    // Update app version
    updateAppVersion();

    // Pastikan elemen sitesList ada
    if (!sitesList) {
      sitesList = document.getElementById("sitesList");
      if (!sitesList) {
        console.error("Element sitesList tidak ditemukan di updateUI");
        const sitesSection = document.getElementById("sitesSection");
        if (sitesSection) {
          sitesSection.innerHTML =
            '<div class="sites-grid" id="sitesList"></div>';
          sitesList = document.getElementById("sitesList");
        }
      }
    }

    // Load sites jika belum ada data
    if (!availableSites || availableSites.length === 0) {
      loadSites();
    } else {
      // Jika sudah ada data, langsung render
      console.log("Rendering existing sites:", availableSites.length);
      renderSites();
    }

    // Ensure scrolling works properly
    enableSmoothScrolling();
  } else {
    // Show login screen
    if (loginScreen) loginScreen.style.display = "block";
    if (mainScreen) mainScreen.style.display = "none";
  }
}

function updateSubscriptionInfo() {
  if (!currentUser || !subscriptionInfo) return;

  if (currentUser.subscription_expires_at) {
    // Parse tanggal dengan format yang benar dari server (YYYY-MM-DD HH:mm:ss)
    const expiryDate = new Date(
      currentUser.subscription_expires_at.replace(" ", "T")
    );
    const now = new Date();

    // Validasi apakah tanggal valid
    if (isNaN(expiryDate.getTime())) {
      console.error(
        "Invalid subscription expiry date:",
        currentUser.subscription_expires_at
      );
      subscriptionInfo.textContent = "Tanggal tidak valid";
      subscriptionInfo.className = "subscription-info danger";
      return;
    }

    // Hitung selisih hari dengan lebih akurat
    const timeDiff = expiryDate.getTime() - now.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    let subscriptionText = "";
    let className = "";

    if (daysLeft < 0) {
      subscriptionText = "Langganan berakhir";
      className = "danger";
    } else if (daysLeft === 0) {
      subscriptionText = "Berakhir hari ini";
      className = "danger";
    } else if (daysLeft <= 7) {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "warning";
    } else if (daysLeft > 365) {
      // Jika lebih dari 1 tahun, tampilkan sebagai unlimited dengan tanggal berakhir
      const options = { year: "numeric", month: "short", day: "numeric" };
      const formattedDate = expiryDate.toLocaleDateString("id-ID", options);
      subscriptionText = `Unlimited (${formattedDate})`;
      className = "";
    } else {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "";
    }

    subscriptionInfo.textContent = subscriptionText;
    subscriptionInfo.className = `subscription-info ${className}`;
  } else {
    // Jika tidak ada tanggal berakhir, cek status langganan
    if (
      currentUser.subscription_status === "active" ||
      currentUser.subscription_plan
    ) {
      subscriptionInfo.textContent = `Unlimited (${
        currentUser.subscription_plan || "Active"
      })`;
    } else {
      subscriptionInfo.textContent = "Unlimited";
    }
    subscriptionInfo.className = "subscription-info";
  }
}

function updateAppVersion() {
  if (!appVersion) return;

  // Get version from manifest
  const manifest = chrome.runtime.getManifest();
  if (manifest && manifest.version) {
    appVersion.textContent = `Versi ${manifest.version}`;
  }
}

// Storage functions
async function saveAuth(authData) {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.set({ satuPintuAuth: authData }, () => {
        if (chrome.runtime.lastError) {
          console.warn("Storage save error:", chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      reject(new Error("Extension context invalidated"));
    }
  });
}

async function getStoredAuth() {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.get(["satuPintuAuth"], (result) => {
        if (chrome.runtime.lastError) {
          console.warn("Storage get error:", chrome.runtime.lastError.message);
          resolve(null); // Return null instead of rejecting for get operations
        } else {
          resolve(result.satuPintuAuth || null);
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      resolve(null); // Return null instead of rejecting for get operations
    }
  });
}

async function clearStoredAuth() {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.remove(["satuPintuAuth"], () => {
        if (chrome.runtime.lastError) {
          console.warn(
            "Storage clear error:",
            chrome.runtime.lastError.message
          );
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      reject(new Error("Extension context invalidated"));
    }
  });
}

// Full page loading animation
function showFullPageLoading(
  show,
  message = "Memuat data situs...",
  subtext = "Mohon tunggu sebentar"
) {
  if (fullPageLoader) {
    if (show) {
      // Update loading text
      const loaderText = fullPageLoader.querySelector(".loader-text");
      const loaderSubtext = fullPageLoader.querySelector(".loader-subtext");
      if (loaderText) loaderText.textContent = message;
      if (loaderSubtext) loaderSubtext.textContent = subtext;

      fullPageLoader.style.display = "flex";
      // Disable all interactive elements
      if (loginBtn) loginBtn.disabled = true;
      if (logoutBtn) logoutBtn.disabled = true;
      if (emailInput) emailInput.disabled = true;
      if (passwordInput) passwordInput.disabled = true;
    } else {
      fullPageLoader.style.display = "none";
      // Re-enable interactive elements
      if (loginBtn) loginBtn.disabled = false;
      if (logoutBtn) logoutBtn.disabled = false;
      if (emailInput) emailInput.disabled = false;
      if (passwordInput) passwordInput.disabled = false;
    }
  }
}

// Get cached data from background script
async function getCachedData() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(
      {
        action: "getCachedData",
      },
      (response) => {
        if (response && response.userSession) {
          console.log(
            "Retrieved cached session:",
            response.userSession.sessionId
          );
          console.log(
            "Cached sites count:",
            response.sitesData?.data?.length || 0
          );
          console.log(
            "Cached cookies files:",
            Object.keys(response.cookiesFolder || {}).length
          );
          resolve(response);
        } else {
          resolve(null);
        }
      }
    );
  });
}

// Footer button handlers
function setupFooterHandlers() {
  const helpBtn = document.getElementById("helpBtn");
  const infoFaqBtn = document.getElementById("infoFaqBtn");

  if (helpBtn) {
    helpBtn.addEventListener("click", function () {
      showHelpModal();
    });
  }

  if (infoFaqBtn) {
    infoFaqBtn.addEventListener("click", function () {
      showInfoFaqModal();
    });
  }

  // Load important info for scrolling text
  loadImportantInfo();
}

// Load important info from API
async function loadImportantInfo() {
  console.log("Loading important info from API...");

  try {
    const url = `${API_BASE_URL}/important-info`;
    console.log("Fetching from URL:", url);
    console.log("Current token available:", !!currentToken);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: currentToken ? `Bearer ${currentToken}` : "",
      },
    });

    console.log("API Response status:", response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log("API Response data:", data);

      if (data.success && data.data) {
        console.log("Found important info data, updating scrolling text");
        updateScrollingText(data.data);
        return;
      } else {
        console.warn("API response success but no data found:", data);
      }
    } else {
      console.warn(
        "Failed to load important info - HTTP",
        response.status,
        response.statusText
      );
      const errorText = await response.text();
      console.warn("Error response:", errorText);
    }
  } catch (error) {
    console.error("Error loading important info:", error);
  }

  // Use default text if API fails or no data
  console.log("Using default text for scrolling info");
  updateScrollingText(null);
}

// Update scrolling text content
function updateScrollingText(importantInfoData) {
  const scrollingTextElement = document.getElementById("scrollingText");
  if (!scrollingTextElement) return;

  let scrollingContent = "";

  // Try to find active important info from database
  if (importantInfoData && Array.isArray(importantInfoData)) {
    const activeInfo = importantInfoData.find((info) => info.is_active);

    if (activeInfo) {
      // Format the content for scrolling text
      if (activeInfo.title) {
        scrollingContent += `📢 ${activeInfo.title}`;
      }

      if (activeInfo.content) {
        if (scrollingContent) scrollingContent += ": ";
        scrollingContent += activeInfo.content;
      }

      // Add additional info if available
      if (activeInfo.additional_info) {
        scrollingContent += ` • ${activeInfo.additional_info}`;
      }

      console.log(
        "Updated scrolling text with important info:",
        activeInfo.title
      );
    }
  }

  // Use default text if no active info found
  if (!scrollingContent) {
    scrollingContent =
      "📢 Informasi Penting: Pastikan ekstensi selalu diperbarui untuk mendapatkan fitur terbaru dan keamanan optimal • Hubungi support jika mengalami kendala • Gunakan fitur dengan bijak";
    console.log("Using default scrolling text");
  }

  // Update the scrolling text
  scrollingTextElement.textContent = scrollingContent;
}

function showHelpModal() {
  const modal = createModal(
    "Bantuan",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">🚀 Cara Menggunakan Ekstensi</h4>
      <ol style="margin: 10px 0; padding-left: 20px;">
        <li>Login dengan akun Satu Pintu Anda</li>
        <li>Pilih aplikasi yang ingin diakses</li>
        <li>Klik untuk otomatis login dan buka aplikasi</li>
      </ol>

      <h4 style="color: #4f46e5;">🔧 Troubleshooting</h4>
      <ul style="margin: 10px 0; padding-left: 20px;">
        <li>Jika login gagal, periksa koneksi internet</li>
        <li>Pastikan ekstensi memiliki izin yang diperlukan</li>
        <li>Coba reload ekstensi jika tidak responsif</li>
        <li>Hubungi support jika masalah berlanjut</li>
      </ul>

      <h4 style="color: #4f46e5;">📞 Kontak Support</h4>
      <p style="margin: 10px 0;">Email: <EMAIL><br>
      WhatsApp: +62 xxx-xxxx-xxxx</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function showInfoFaqModal() {
  const modal = createModal(
    "Informasi & FAQ",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">ℹ️ Tentang Ekstensi</h4>
      <p style="margin: 10px 0;">Ekstensi Satu Pintu memungkinkan Anda mengakses berbagai aplikasi dengan satu kali login. Ekstensi ini menggunakan teknologi cookie injection untuk memberikan pengalaman seamless.</p>

      <h4 style="color: #4f46e5;">❓ FAQ</h4>
      <div style="margin: 10px 0;">
        <strong>Q: Apakah data saya aman?</strong><br>
        A: Ya, semua data dienkripsi dan disimpan secara lokal di browser Anda.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Mengapa perlu login ulang?</strong><br>
        A: Untuk keamanan, session akan expired setelah periode tertentu.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Aplikasi tidak muncul?</strong><br>
        A: Pastikan Anda memiliki akses ke aplikasi tersebut dan langganan masih aktif.
      </div>

      <h4 style="color: #4f46e5;">🔒 Keamanan</h4>
      <p style="margin: 10px 0;">Ekstensi ini menggunakan enkripsi end-to-end dan tidak menyimpan password Anda. Semua komunikasi menggunakan HTTPS.</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function createModal(title, content) {
  const modal = document.createElement("div");
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  `;

  const modalContent = document.createElement("div");
  modalContent.style.cssText = `
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 16px;
    padding: 24px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
  `;

  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h3 style="margin: 0; font-size: 18px; font-weight: 600;">${title}</h3>
      <button id="closeModal" style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      ">×</button>
    </div>
    <div>${content}</div>
  `;

  modal.appendChild(modalContent);

  // Close modal handlers
  const closeBtn = modalContent.querySelector("#closeModal");
  closeBtn.addEventListener("click", () => modal.remove());

  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });

  return modal;
}

async function injectCookiesAndOpenSite(site) {
  try {
    // Validate site data
    if (!site || !site.id || !site.url) {
      throw new Error("Invalid site data");
    }

    // Show initial loading
    showFullPageLoading(
      true,
      `Menyiapkan ${site.name}...`,
      "Mengambil data cookies"
    );
    showStatus(`Memuat data untuk ${site.name}...`, "success");

    // Get cookies for the site
    const siteData = await getSiteCookies(site.id);

    if (!siteData || !siteData.cookies || !siteData.domain) {
      throw new Error("Tidak dapat memuat data cookies");
    }

    // Set cookies
    let successCount = 0;
    let errorCount = 0;

    // Try to use background script for better performance
    try {
      console.log("📤 Sending cookies to background script for bulk setting...");
      const response = await chrome.runtime.sendMessage({
        action: "setCookies",
        cookies: siteData.cookies,
        domain: siteData.domain,
      });

      if (response && response.success) {
        successCount = response.result.success;
        errorCount = response.result.failed;
        console.log("✅ Bulk cookie setting result:", response.result);
      } else {
        throw new Error(response?.error || "Background script failed");
      }
    } catch (error) {
      console.warn("⚠️ Background script failed, falling back to individual setting:", error);

      // Fallback to individual cookie setting
      console.log("🔄 Using fallback method - setting cookies individually...");
      for (let i = 0; i < siteData.cookies.length; i++) {
        const cookie = siteData.cookies[i];
        console.log(`🍪 Processing cookie ${i + 1}/${siteData.cookies.length}:`, cookie);

        try {
          await setCookieWithExtensionAPI(cookie, siteData.domain);
          successCount++;
          console.log(`✅ Cookie ${cookie.name} set successfully`);
        } catch (error) {
          errorCount++;
          console.error("❌ Cookie set error:", error);
          console.warn("📋 Cookie data:", cookie);
        }
      }
    }

    // Show detailed results
    if (successCount > 0) {
      const statusMessage = `${successCount}/${siteData.cookies.length} data berhasil diproses${errorCount > 0 ? ` (${errorCount} gagal)` : ""}. Membuka ${site.name}...`;
      showStatus(statusMessage, errorCount > 0 ? "warning" : "success");
      console.log(`🎯 Cookie import completed: ${successCount} success, ${errorCount} failed`);
    } else {
      showStatus(`Gagal memproses semua data. Membuka ${site.name} tanpa data tambahan...`, "error");
      console.error("❌ All cookies failed to set");
    }

    // Add small delay before opening site to ensure cookies are properly set
    console.log("⏳ Waiting 300ms before opening site to ensure cookies are set...");
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Open site in new tab
    showFullPageLoading(true, `Membuka ${site.name}...`, "Menyiapkan tab baru");

    try {
      console.log(`🌐 Opening site in new tab: ${site.url}`);
      chrome.tabs.create({ url: site.url });
      console.log("✅ Site opened successfully");

      // Add small delay to show completion
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.warn("⚠️ Chrome tabs API error:", error);
      // Fallback: open in current window
      console.log("🔄 Using fallback method to open site...");
      window.open(site.url, "_blank");
    }

    // Hide loading
    showFullPageLoading(false);
  } catch (error) {
    console.error("Error injecting cookies and opening site:", error);
    showFullPageLoading(false);
    showStatus(`Error: ${error.message}`, "error");
  }
}

async function setCookieWithExtensionAPI(cookie, siteDomain) {
  return new Promise((resolve, reject) => {
    // Validate required fields
    if (!cookie.name || cookie.value === undefined) {
      reject(new Error("Cookie name and value are required"));
      return;
    }

    // Clean siteDomain (remove protocol if present)
    const cleanSiteDomain = siteDomain
      .replace(/^https?:\/\//, "")
      .replace(/\/.*$/, "");

    // Determine protocol based on secure flag
    const protocol = cookie.secure !== false ? "https" : "http";

    // Prepare cookie for Chrome API
    const cookieDetails = {
      url: `${protocol}://${cleanSiteDomain}`,
      name: cookie.name,
      value: cookie.value,
    };

    console.log(`Using site domain: ${cleanSiteDomain}, protocol: ${protocol}`);

    // Handle domain - prioritize cookie.domain, fallback to siteDomain
    if (cookie.domain) {
      // Clean domain (remove protocol if present)
      let cleanDomain = cookie.domain
        .replace(/^https?:\/\//, "")
        .replace(/\/.*$/, "");

      // If domain doesn't start with dot and is not the exact siteDomain, add dot for subdomain support
      if (!cleanDomain.startsWith(".") && cleanDomain !== cleanSiteDomain) {
        cookieDetails.domain = `.${cleanDomain}`;
      } else {
        cookieDetails.domain = cleanDomain;
      }
    } else {
      cookieDetails.domain = cleanSiteDomain;
    }

    console.log(
      `Domain mapping: ${cookie.domain || "undefined"} -> ${
        cookieDetails.domain
      }`
    );

    // Handle path
    cookieDetails.path = cookie.path || "/";

    // Handle secure flag
    if (cookie.secure !== undefined) {
      cookieDetails.secure = cookie.secure;
    } else {
      // Default to true for HTTPS sites
      cookieDetails.secure = true;
    }

    // Handle httpOnly flag
    if (cookie.httpOnly !== undefined) {
      cookieDetails.httpOnly = cookie.httpOnly;
    }

    // Handle sameSite
    if (cookie.sameSite) {
      const validSameSite = ["unspecified", "no_restriction", "lax", "strict"];
      if (validSameSite.includes(cookie.sameSite)) {
        cookieDetails.sameSite = cookie.sameSite;
      } else if (cookie.sameSite === "none") {
        cookieDetails.sameSite = "no_restriction";
      } else {
        // Default mapping for common values
        cookieDetails.sameSite = "lax";
      }
    }

    // Handle expiration date
    if (cookie.expirationDate) {
      // Handle both Unix timestamp and Date object
      if (typeof cookie.expirationDate === "number") {
        cookieDetails.expirationDate = cookie.expirationDate;
      } else if (cookie.expirationDate instanceof Date) {
        cookieDetails.expirationDate = Math.floor(
          cookie.expirationDate.getTime() / 1000
        );
      }
    } else {
      // Set expiration to 1 year from now if not specified
      cookieDetails.expirationDate =
        Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60;
    }

    console.log("Setting cookie:", cookieDetails);

    // Function to attempt setting cookie
    const attemptSetCookie = (details, isRetry = false) => {
      try {
        chrome.cookies.set(details, (result) => {
          if (chrome.runtime.lastError) {
            console.warn("Cookie set error:", chrome.runtime.lastError.message);
            console.warn("Cookie details:", details);

            // If HTTPS failed and this is not a retry, try with HTTP
            if (!isRetry && details.url.startsWith("https://")) {
              console.log("Retrying with HTTP...");
              const httpDetails = { ...details };
              httpDetails.url = details.url.replace("https://", "http://");
              httpDetails.secure = false;
              attemptSetCookie(httpDetails, true);
            } else {
              reject(new Error(chrome.runtime.lastError.message));
            }
          } else {
            console.log("Cookie set successfully:", result);
            resolve(result);
          }
        });
      } catch (error) {
        console.warn("Chrome cookies API error:", error);
        reject(new Error("Extension context invalidated"));
      }
    };

    // Start the attempt
    attemptSetCookie(cookieDetails);
  });
}

function showLoginScreen() {
  // Ensure loading is hidden when showing login screen
  showFullPageLoading(false);

  // Reset opacity untuk animasi berikutnya
  loginScreen.style.opacity = "0";
  loginScreen.style.transition = "opacity 0.5s ease-in";

  // Tampilkan login screen dengan animasi
  loginScreen.style.display = "block";

  // Trigger reflow untuk memastikan transisi berjalan
  loginScreen.offsetHeight;

  // Mulai animasi fade-in
  loginScreen.style.opacity = "1";

  // Sembunyikan main screen
  mainScreen.style.display = "none";
}

function showMainScreen() {
  // Pastikan main screen ditampilkan dengan display flex
  mainScreen.style.display = "flex";

  // Hanya ubah display loginScreen jika opasitasnya bukan 0
  // (jika tidak sedang dalam animasi fade-out)
  if (loginScreen.style.opacity !== "0") {
    loginScreen.style.display = "none";
  }

  // Update user info
  userName.textContent = currentUser.name;
  userRole.textContent = currentUser.role;
  userRole.className = `role-badge ${currentUser.role}`;

  // Update app version
  updateAppVersion();

  // Load important info for scrolling text
  loadImportantInfo();
}

function showLoginStatus(message, type) {
  loginStatus.textContent = message;
  loginStatus.className = `status ${type}`;
  loginStatus.style.display = "block";

  setTimeout(() => {
    loginStatus.style.display = "none";
  }, 3000);
}

function showStatus(message, type) {
  if (statusDiv) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = "block";

    setTimeout(() => {
      statusDiv.style.display = "none";
    }, 3000);
  }
}

function showTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "block";
  }
}

function hideTroubleshooting() {
  if (troubleshootingDiv) {
    troubleshootingDiv.style.display = "none";
  }
}

// Update UI based on authentication status
function updateUI() {
  if (currentUser && currentToken) {
    // Show main screen
    if (loginScreen) loginScreen.style.display = "none";
    if (mainScreen) mainScreen.style.display = "block";

    // Update user info
    if (userName) userName.textContent = currentUser.name;
    if (userRole) {
      userRole.textContent =
        currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
      userRole.className = `role-badge ${currentUser.role}`;
    }

    // Update subscription info
    updateSubscriptionInfo();

    // Update app version
    updateAppVersion();

    // Pastikan elemen sitesList ada
    if (!sitesList) {
      sitesList = document.getElementById("sitesList");
      if (!sitesList) {
        console.error("Element sitesList tidak ditemukan di updateUI");
        const sitesSection = document.getElementById("sitesSection");
        if (sitesSection) {
          sitesSection.innerHTML =
            '<div class="sites-grid" id="sitesList"></div>';
          sitesList = document.getElementById("sitesList");
        }
      }
    }

    // Load sites jika belum ada data
    if (!availableSites || availableSites.length === 0) {
      loadSites();
    } else {
      // Jika sudah ada data, langsung render
      console.log("Rendering existing sites:", availableSites.length);
      renderSites();
    }

    // Ensure scrolling works properly
    enableSmoothScrolling();
  } else {
    // Show login screen
    if (loginScreen) loginScreen.style.display = "block";
    if (mainScreen) mainScreen.style.display = "none";
  }
}

function updateSubscriptionInfo() {
  if (!currentUser || !subscriptionInfo) return;

  if (currentUser.subscription_expires_at) {
    // Parse tanggal dengan format yang benar dari server (YYYY-MM-DD HH:mm:ss)
    const expiryDate = new Date(
      currentUser.subscription_expires_at.replace(" ", "T")
    );
    const now = new Date();

    // Validasi apakah tanggal valid
    if (isNaN(expiryDate.getTime())) {
      console.error(
        "Invalid subscription expiry date:",
        currentUser.subscription_expires_at
      );
      subscriptionInfo.textContent = "Tanggal tidak valid";
      subscriptionInfo.className = "subscription-info danger";
      return;
    }

    // Hitung selisih hari dengan lebih akurat
    const timeDiff = expiryDate.getTime() - now.getTime();
    const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    let subscriptionText = "";
    let className = "";

    if (daysLeft < 0) {
      subscriptionText = "Langganan berakhir";
      className = "danger";
    } else if (daysLeft === 0) {
      subscriptionText = "Berakhir hari ini";
      className = "danger";
    } else if (daysLeft <= 7) {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "warning";
    } else if (daysLeft > 365) {
      // Jika lebih dari 1 tahun, tampilkan sebagai unlimited dengan tanggal berakhir
      const options = { year: "numeric", month: "short", day: "numeric" };
      const formattedDate = expiryDate.toLocaleDateString("id-ID", options);
      subscriptionText = `Unlimited (${formattedDate})`;
      className = "";
    } else {
      subscriptionText = `${daysLeft} hari lagi`;
      className = "";
    }

    subscriptionInfo.textContent = subscriptionText;
    subscriptionInfo.className = `subscription-info ${className}`;
  } else {
    // Jika tidak ada tanggal berakhir, cek status langganan
    if (
      currentUser.subscription_status === "active" ||
      currentUser.subscription_plan
    ) {
      subscriptionInfo.textContent = `Unlimited (${
        currentUser.subscription_plan || "Active"
      })`;
    } else {
      subscriptionInfo.textContent = "Unlimited";
    }
    subscriptionInfo.className = "subscription-info";
  }
}

function updateAppVersion() {
  if (!appVersion) return;

  // Get version from manifest
  const manifest = chrome.runtime.getManifest();
  if (manifest && manifest.version) {
    appVersion.textContent = `Versi ${manifest.version}`;
  }
}

// Storage functions
async function saveAuth(authData) {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.set({ satuPintuAuth: authData }, () => {
        if (chrome.runtime.lastError) {
          console.warn("Storage save error:", chrome.runtime.lastError.message);
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      reject(new Error("Extension context invalidated"));
    }
  });
}

async function getStoredAuth() {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.get(["satuPintuAuth"], (result) => {
        if (chrome.runtime.lastError) {
          console.warn("Storage get error:", chrome.runtime.lastError.message);
          resolve(null); // Return null instead of rejecting for get operations
        } else {
          resolve(result.satuPintuAuth || null);
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      resolve(null); // Return null instead of rejecting for get operations
    }
  });
}

async function clearStoredAuth() {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.remove(["satuPintuAuth"], () => {
        if (chrome.runtime.lastError) {
          console.warn(
            "Storage clear error:",
            chrome.runtime.lastError.message
          );
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    } catch (error) {
      console.warn("Chrome storage API error:", error);
      reject(new Error("Extension context invalidated"));
    }
  });
}

// Full page loading animation
function showFullPageLoading(
  show,
  message = "Memuat data situs...",
  subtext = "Mohon tunggu sebentar"
) {
  if (fullPageLoader) {
    if (show) {
      // Update loading text
      const loaderText = fullPageLoader.querySelector(".loader-text");
      const loaderSubtext = fullPageLoader.querySelector(".loader-subtext");
      if (loaderText) loaderText.textContent = message;
      if (loaderSubtext) loaderSubtext.textContent = subtext;

      fullPageLoader.style.display = "flex";
      // Disable all interactive elements
      if (loginBtn) loginBtn.disabled = true;
      if (logoutBtn) logoutBtn.disabled = true;
      if (emailInput) emailInput.disabled = true;
      if (passwordInput) passwordInput.disabled = true;
    } else {
      fullPageLoader.style.display = "none";
      // Re-enable interactive elements
      if (loginBtn) loginBtn.disabled = false;
      if (logoutBtn) logoutBtn.disabled = false;
      if (emailInput) emailInput.disabled = false;
      if (passwordInput) passwordInput.disabled = false;
    }
  }
}

// Get cached data from background script
async function getCachedData() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(
      {
        action: "getCachedData",
      },
      (response) => {
        if (response && response.userSession) {
          console.log(
            "Retrieved cached session:",
            response.userSession.sessionId
          );
          console.log(
            "Cached sites count:",
            response.sitesData?.data?.length || 0
          );
          console.log(
            "Cached cookies files:",
            Object.keys(response.cookiesFolder || {}).length
          );
          resolve(response);
        } else {
          resolve(null);
        }
      }
    );
  });
}

// Footer button handlers
function setupFooterHandlers() {
  const helpBtn = document.getElementById("helpBtn");
  const infoFaqBtn = document.getElementById("infoFaqBtn");

  if (helpBtn) {
    helpBtn.addEventListener("click", function () {
      showHelpModal();
    });
  }

  if (infoFaqBtn) {
    infoFaqBtn.addEventListener("click", function () {
      showInfoFaqModal();
    });
  }

  // Load important info for scrolling text
  loadImportantInfo();
}

// Load important info from API
async function loadImportantInfo() {
  console.log("Loading important info from API...");

  try {
    const url = `${API_BASE_URL}/important-info`;
    console.log("Fetching from URL:", url);
    console.log("Current token available:", !!currentToken);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: currentToken ? `Bearer ${currentToken}` : "",
      },
    });

    console.log("API Response status:", response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log("API Response data:", data);

      if (data.success && data.data) {
        console.log("Found important info data, updating scrolling text");
        updateScrollingText(data.data);
        return;
      } else {
        console.warn("API response success but no data found:", data);
      }
    } else {
      console.warn(
        "Failed to load important info - HTTP",
        response.status,
        response.statusText
      );
      const errorText = await response.text();
      console.warn("Error response:", errorText);
    }
  } catch (error) {
    console.error("Error loading important info:", error);
  }

  // Use default text if API fails or no data
  console.log("Using default text for scrolling info");
  updateScrollingText(null);
}

// Update scrolling text content
function updateScrollingText(importantInfoData) {
  const scrollingTextElement = document.getElementById("scrollingText");
  if (!scrollingTextElement) return;

  let scrollingContent = "";

  // Try to find active important info from database
  if (importantInfoData && Array.isArray(importantInfoData)) {
    const activeInfo = importantInfoData.find((info) => info.is_active);

    if (activeInfo) {
      // Format the content for scrolling text
      if (activeInfo.title) {
        scrollingContent += `📢 ${activeInfo.title}`;
      }

      if (activeInfo.content) {
        if (scrollingContent) scrollingContent += ": ";
        scrollingContent += activeInfo.content;
      }

      // Add additional info if available
      if (activeInfo.additional_info) {
        scrollingContent += ` • ${activeInfo.additional_info}`;
      }

      console.log(
        "Updated scrolling text with important info:",
        activeInfo.title
      );
    }
  }

  // Use default text if no active info found
  if (!scrollingContent) {
    scrollingContent =
      "📢 Informasi Penting: Pastikan ekstensi selalu diperbarui untuk mendapatkan fitur terbaru dan keamanan optimal • Hubungi support jika mengalami kendala • Gunakan fitur dengan bijak";
    console.log("Using default scrolling text");
  }

  // Update the scrolling text
  scrollingTextElement.textContent = scrollingContent;
}

function showHelpModal() {
  const modal = createModal(
    "Bantuan",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">🚀 Cara Menggunakan Ekstensi</h4>
      <ol style="margin: 10px 0; padding-left: 20px;">
        <li>Login dengan akun Satu Pintu Anda</li>
        <li>Pilih aplikasi yang ingin diakses</li>
        <li>Klik untuk otomatis login dan buka aplikasi</li>
      </ol>

      <h4 style="color: #4f46e5;">🔧 Troubleshooting</h4>
      <ul style="margin: 10px 0; padding-left: 20px;">
        <li>Jika login gagal, periksa koneksi internet</li>
        <li>Pastikan ekstensi memiliki izin yang diperlukan</li>
        <li>Coba reload ekstensi jika tidak responsif</li>
        <li>Hubungi support jika masalah berlanjut</li>
      </ul>

      <h4 style="color: #4f46e5;">📞 Kontak Support</h4>
      <p style="margin: 10px 0;">Email: <EMAIL><br>
      WhatsApp: +62 xxx-xxxx-xxxx</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function showInfoFaqModal() {
  const modal = createModal(
    "Informasi & FAQ",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">ℹ️ Tentang Ekstensi</h4>
      <p style="margin: 10px 0;">Ekstensi Satu Pintu memungkinkan Anda mengakses berbagai aplikasi dengan satu kali login. Ekstensi ini menggunakan teknologi cookie injection untuk memberikan pengalaman seamless.</p>

      <h4 style="color: #4f46e5;">❓ FAQ</h4>
      <div style="margin: 10px 0;">
        <strong>Q: Apakah data saya aman?</strong><br>
        A: Ya, semua data dienkripsi dan disimpan secara lokal di browser Anda.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Mengapa perlu login ulang?</strong><br>
        A: Untuk keamanan, session akan expired setelah periode tertentu.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Aplikasi tidak muncul?</strong><br>
        A: Pastikan Anda memiliki akses ke aplikasi tersebut dan langganan masih aktif.
      </div>

      <h4 style="color: #4f46e5;">🔒 Keamanan</h4>
      <p style="margin: 10px 0;">Ekstensi ini menggunakan enkripsi end-to-end dan tidak menyimpan password Anda. Semua komunikasi menggunakan HTTPS.</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function createModal(title, content) {
  const modal = document.createElement("div");
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  `;

  const modalContent = document.createElement("div");
  modalContent.style.cssText = `
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 16px;
    padding: 24px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
  `;

  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h3 style="margin: 0; font-size: 18px; font-weight: 600;">${title}</h3>
      <button id="closeModal" style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      ">×</button>
    </div>
    <div>${content}</div>
  `;

  modal.appendChild(modalContent);

  // Close modal handlers
  const closeBtn = modalContent.querySelector("#closeModal");
  closeBtn.addEventListener("click", () => modal.remove());

  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });

  return modal;
}

function showInfoFaqModal() {
  const modal = createModal(
    "Informasi & FAQ",
    `
    <div style="text-align: left; line-height: 1.6;">
      <h4 style="margin-top: 0; color: #4f46e5;">ℹ️ Tentang Ekstensi</h4>
      <p style="margin: 10px 0;">Ekstensi Satu Pintu memungkinkan Anda mengakses berbagai aplikasi dengan satu kali login. Ekstensi ini menggunakan teknologi cookie injection untuk memberikan pengalaman seamless.</p>

      <h4 style="color: #4f46e5;">❓ FAQ</h4>
      <div style="margin: 10px 0;">
        <strong>Q: Apakah data saya aman?</strong><br>
        A: Ya, semua data dienkripsi dan disimpan secara lokal di browser Anda.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Mengapa perlu login ulang?</strong><br>
        A: Untuk keamanan, session akan expired setelah periode tertentu.
      </div>

      <div style="margin: 10px 0;">
        <strong>Q: Aplikasi tidak muncul?</strong><br>
        A: Pastikan Anda memiliki akses ke aplikasi tersebut dan langganan masih aktif.
      </div>

      <h4 style="color: #4f46e5;">🔒 Keamanan</h4>
      <p style="margin: 10px 0;">Ekstensi ini menggunakan enkripsi end-to-end dan tidak menyimpan password Anda. Semua komunikasi menggunakan HTTPS.</p>
    </div>
  `
  );

  document.body.appendChild(modal);
}

function createModal(title, content) {
  const modal = document.createElement("div");
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  `;

  const modalContent = document.createElement("div");
  modalContent.style.cssText = `
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 16px;
    padding: 24px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
  `;

  modalContent.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h3 style="margin: 0; font-size: 18px; font-weight: 600;">${title}</h3>
      <button id="closeModal" style="
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
      ">×</button>
    </div>
    <div>${content}</div>
  `;

  modal.appendChild(modalContent);

  // Close modal handlers
  const closeBtn = modalContent.querySelector("#closeModal");
  closeBtn.addEventListener("click", () => modal.remove());

  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });

  return modal;
}
