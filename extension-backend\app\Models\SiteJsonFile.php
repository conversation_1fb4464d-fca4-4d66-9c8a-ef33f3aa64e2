<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SiteJsonFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'site_id',
        'name',
        'file_path',
        'description',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the site that owns the JSON file.
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get cookies from the JSON file
     */
    public function getCookiesFromFile(): array
    {
        if (!$this->file_path) {
            return [];
        }

        $filePath = storage_path('app/public/' . $this->file_path);
        
        if (!file_exists($filePath)) {
            return [];
        }

        $content = file_get_contents($filePath);
        $cookies = json_decode($content, true);

        return is_array($cookies) ? $cookies : [];
    }
}
