<?php

namespace App\Filament\Resources\ApiV2\SiteResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class JsonFilesRelationManager extends RelationManager
{
    protected static string $relationship = 'jsonFiles';

    protected static ?string $title = 'File JSON';

    protected static ?string $modelLabel = 'File JSON';

    protected static ?string $pluralModelLabel = 'File JSON';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama File')
                    ->required()
                    ->maxLength(255)
                    ->helperText('Contoh: ChatGPT Pro 1, ChatGPT Pro 2'),
                
                Forms\Components\FileUpload::make('file_path')
                    ->label('File JSON')
                    ->acceptedFileTypes(['application/json'])
                    ->directory('json-files')
                    ->visibility('public')
                    ->required()
                    ->helperText('Upload file JSON yang berisi cookies'),
                
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->rows(3)
                    ->helperText('Deskripsi opsional untuk file ini'),
                
                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama File')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('file_path')
                    ->label('File')
                    ->limit(30)
                    ->tooltip(fn ($record) => $record->file_path),
                
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->toggleable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah File JSON'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}