<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Satu Pintu</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-800">Satu Pintu Dashboard</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">{{ $user->name }}</span>
                    <span class="px-2 py-1 text-xs font-medium rounded-full 
                        {{ $user->role === 'premium' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                        {{ ucfirst($user->role) }}
                    </span>
                    <form method="POST" action="{{ route('logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Selamat Datang!</h2>
                    
                    @if(session('success'))
                    <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">
                                    {{ session('success') }}
                                </h3>
                                <div class="mt-2 text-sm text-green-700">
                                    Anda dapat menutup tab ini dan kembali ke ekstensi browser untuk mulai menggunakan Cookie Manager.
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">
                                    Informasi Akun
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Anda telah berhasil login. Silakan kembali ke extension browser untuk menggunakan fitur Satu Pintu.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-900 mb-2">Informasi Akun</h3>
                            <dl class="space-y-1">
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Nama:</dt>
                                    <dd class="text-sm font-medium text-gray-900">{{ $user->name }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Email:</dt>
                                    <dd class="text-sm font-medium text-gray-900">{{ $user->email }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Role:</dt>
                                    <dd class="text-sm font-medium text-gray-900">{{ ucfirst($user->role) }}</dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm text-gray-600">Status:</dt>
                                    <dd class="text-sm font-medium text-green-600">Aktif</dd>
                                </div>
                            </dl>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-900 mb-2">Akses Extension</h3>
                            <p class="text-sm text-gray-600 mb-3">
                                Untuk menggunakan extension Satu Pintu:
                            </p>
                            <ol class="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                                <li>Buka extension di browser Anda</li>
                                <li>Klik tombol "Login" di extension</li>
                                <li>Anda akan diarahkan ke halaman ini</li>
                                <li>Setelah login, kembali ke extension</li>
                            </ol>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">Akses Situs</h3>
                        <p class="text-sm text-gray-600">
                            @if($user->role === 'elite')
                                Sebagai pengguna Elite, Anda memiliki akses ke situs-situs kategori Elite.
                            @else
                                Sebagai pengguna Premium, Anda memiliki akses ke semua situs (Elite dan Premium).
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>