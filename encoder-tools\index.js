#!/usr/bin/env node

/**
 * Encoder Tools - CLI Interface
 * 
 * Antarmuka command-line untuk tools encoding
 */

const fs = require('fs');
const path = require('path');
const encoder = require('./encoder');

// Fungsi untuk menampilkan bantuan
function showHelp() {
  console.log(`
  Encoder Tools - Alat untuk encoding JSON, HTML, dan JavaScript

  Penggunaan:
    node index.js <perintah> [opsi]

  Perintah:
    encode-file <path-file> [output-filename]  Encode file tunggal
    encode-folder <path-folder>                Encode semua file dalam folder
    encode-json <string-json>                  Encode string JSON
    encode-html <string-html>                  Encode string HTML
    encode-js <string-js>                      Encode string JavaScript
    help                                       Tampilkan bantuan ini

  Contoh:
    node index.js encode-file ./path/to/file.json
    node index.js encode-folder ./path/to/folder
    node index.js encode-json '{"name":"<PERSON>"}'
    node index.js encode-html '<div>Hello</div>'
    node index.js encode-js 'function test() { return true; }'
  `);
}

// Fungsi utama
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command || command === 'help') {
    showHelp();
    return;
  }

  try {
    switch (command) {
      case 'encode-file':
        if (!args[1]) {
          console.error('Error: Path file tidak disediakan');
          showHelp();
          return;
        }
        const filePath = path.resolve(args[1]);
        const outputFilename = args[2] || null;
        encoder.encodeFile(filePath, outputFilename);
        break;

      case 'encode-folder':
        if (!args[1]) {
          console.error('Error: Path folder tidak disediakan');
          showHelp();
          return;
        }
        const folderPath = path.resolve(args[1]);
        encoder.encodeFolder(folderPath);
        break;

      case 'encode-json':
        if (!args[1]) {
          console.error('Error: String JSON tidak disediakan');
          showHelp();
          return;
        }
        const jsonString = args[1];
        const outputJson = args[2] || 'encoded_json.txt';
        try {
          const encodedJson = encoder.encodeJSON(jsonString, outputJson);
          console.log('\nHasil encoding JSON:');
          console.log(encodedJson);
        } catch (error) {
          console.error(`Error saat encoding JSON: ${error.message}`);
          process.exit(1);
        }
        break;

      case 'encode-html':
        if (!args[1]) {
          console.error('Error: String HTML tidak disediakan');
          showHelp();
          return;
        }
        const htmlString = args[1];
        const outputHtml = args[2] || 'encoded_html.txt';
        const encodedHtml = encoder.encodeHTML(htmlString, outputHtml);
        console.log('\nHasil encoding HTML:');
        console.log(encodedHtml);
        break;

      case 'encode-js':
        if (!args[1]) {
          console.error('Error: String JavaScript tidak disediakan');
          showHelp();
          return;
        }
        const jsString = args[1];
        const outputJs = args[2] || 'encoded_js.txt';
        const encodedJs = encoder.encodeJavaScript(jsString, outputJs);
        console.log('\nHasil encoding JavaScript:');
        console.log(encodedJs);
        break;

      default:
        console.error(`Error: Perintah tidak dikenal: ${command}`);
        showHelp();
        break;
    }
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
}

// Jalankan program
main();