/**
 * Encoder Tools
 * 
 * Alat untuk melakukan encoding pada JSON, HTML, dan <PERSON>
 * secara otomatis dan menyimpan hasilnya di folder output.
 */

const fs = require('fs');
const path = require('path');

// Konfigurasi folder
const OUTPUT_FOLDER = path.join(__dirname, 'output');

// Pastikan folder output ada
if (!fs.existsSync(OUTPUT_FOLDER)) {
  fs.mkdirSync(OUTPUT_FOLDER, { recursive: true });
  console.log(`Folder output dibuat di: ${OUTPUT_FOLDER}`);
}

/**
 * Fungsi untuk melakukan encoding pada JSON
 * @param {Object|string} data - Data JSON yang akan di-encode (bisa berupa string atau objek)
 * @param {string} outputFilename - Nama file output (opsional)
 * @returns {string} - Hasil encoding dalam format string
 */
function encodeJSON(data, outputFilename = null) {
  try {
    // Pastikan data dalam bentuk string
    let jsonString = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    
    // Encode JSON string
    const encodedJSON = encodeURIComponent(jsonString);
    
    // Simpan ke file jika outputFilename disediakan
    if (outputFilename) {
      // Pastikan nama file valid
      const safeFilename = outputFilename.replace(/[\\/:*?"<>|]/g, '_');
      const outputPath = path.join(OUTPUT_FOLDER, safeFilename);
      fs.writeFileSync(outputPath, encodedJSON);
      console.log(`JSON berhasil di-encode dan disimpan di: ${outputPath}`);
    }
    
    return encodedJSON;
  } catch (error) {
    console.error('Error saat encoding JSON:', error);
    throw error;
  }
}

/**
 * Fungsi untuk melakukan encoding pada HTML
 * @param {string} htmlContent - Konten HTML yang akan di-encode
 * @param {string} outputFilename - Nama file output (opsional)
 * @returns {string} - Hasil encoding dalam format string
 */
function encodeHTML(htmlContent, outputFilename = null) {
  try {
    // Encode HTML content
    const encodedHTML = htmlContent
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
    
    // Simpan ke file jika outputFilename disediakan
    if (outputFilename) {
      // Pastikan nama file valid
      const safeFilename = outputFilename.replace(/[\\/:*?"<>|]/g, '_');
      const outputPath = path.join(OUTPUT_FOLDER, safeFilename);
      fs.writeFileSync(outputPath, encodedHTML);
      console.log(`HTML berhasil di-encode dan disimpan di: ${outputPath}`);
    }
    
    return encodedHTML;
  } catch (error) {
    console.error('Error saat encoding HTML:', error);
    throw error;
  }
}

/**
 * Fungsi untuk melakukan encoding pada JavaScript
 * @param {string} jsContent - Konten JavaScript yang akan di-encode
 * @param {string} outputFilename - Nama file output (opsional)
 * @returns {string} - Hasil encoding dalam format string
 */
function encodeJavaScript(jsContent, outputFilename = null) {
  try {
    // Encode JavaScript content
    const encodedJS = encodeURIComponent(jsContent);
    
    // Simpan ke file jika outputFilename disediakan
    if (outputFilename) {
      const outputPath = path.join(OUTPUT_FOLDER, outputFilename);
      fs.writeFileSync(outputPath, encodedJS);
      console.log(`JavaScript berhasil di-encode dan disimpan di: ${outputPath}`);
    }
    
    return encodedJS;
  } catch (error) {
    console.error('Error saat encoding JavaScript:', error);
    throw error;
  }
}

/**
 * Fungsi untuk membaca file dan melakukan encoding berdasarkan tipe file
 * @param {string} filePath - Path ke file yang akan di-encode
 * @param {string} outputFilename - Nama file output (opsional)
 * @returns {string} - Hasil encoding dalam format string
 */
function encodeFile(filePath, outputFilename = null) {
  try {
    // Baca file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const fileExt = path.extname(filePath).toLowerCase();
    
    // Tentukan nama file output jika tidak disediakan
    if (!outputFilename) {
      const fileName = path.basename(filePath);
      outputFilename = `encoded_${fileName}`;
    }
    
    // Encode berdasarkan tipe file
    switch (fileExt) {
      case '.json':
        return encodeJSON(fileContent, outputFilename);
      case '.html':
      case '.htm':
        return encodeHTML(fileContent, outputFilename);
      case '.js':
        return encodeJavaScript(fileContent, outputFilename);
      default:
        throw new Error(`Tipe file tidak didukung: ${fileExt}`);
    }
  } catch (error) {
    console.error('Error saat encoding file:', error);
    throw error;
  }
}

/**
 * Fungsi untuk melakukan encoding pada semua file dalam folder
 * @param {string} folderPath - Path ke folder yang berisi file-file yang akan di-encode
 * @param {Array<string>} extensions - Array ekstensi file yang akan di-encode (opsional)
 */
function encodeFolder(folderPath, extensions = ['.json', '.html', '.htm', '.js']) {
  try {
    // Baca semua file dalam folder
    const files = fs.readdirSync(folderPath);
    
    // Iterasi setiap file
    files.forEach(file => {
      const filePath = path.join(folderPath, file);
      const stat = fs.statSync(filePath);
      
      // Jika file adalah folder, lakukan rekursi
      if (stat.isDirectory()) {
        encodeFolder(filePath, extensions);
        return;
      }
      
      // Jika file memiliki ekstensi yang didukung, encode file
      const fileExt = path.extname(file).toLowerCase();
      if (extensions.includes(fileExt)) {
        try {
          encodeFile(filePath);
        } catch (error) {
          console.error(`Error saat encoding file ${file}:`, error);
        }
      }
    });
    
    console.log(`Semua file dalam folder ${folderPath} berhasil di-encode`);
  } catch (error) {
    console.error('Error saat encoding folder:', error);
    throw error;
  }
}

// Export fungsi-fungsi
module.exports = {
  encodeJSON,
  encodeHTML,
  encodeJavaScript,
  encodeFile,
  encodeFolder
};