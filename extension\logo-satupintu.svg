<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="numberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="32" cy="32" r="30" fill="url(#doorGradient)" filter="url(#shadow)"/>
  
  <!-- Door Frame -->
  <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="#ffffff" opacity="0.9"/>
  <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradient)"/>
  
  <!-- Door Handle -->
  <circle cx="38" cy="32" r="2" fill="#fbbf24"/>
  <circle cx="38" cy="32" r="1.5" fill="#f59e0b"/>
  
  <!-- Door Panel Lines -->
  <rect x="22" y="18" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
  <rect x="22" y="22" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
  <rect x="22" y="40" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
  <rect x="22" y="44" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
  
  <!-- Number "1" -->
  <g transform="translate(26, 26)">
    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
          stroke="url(#numberGradient)" 
          stroke-width="3" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"/>
    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
          stroke="#ffffff" 
          stroke-width="1.5" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="15" cy="15" r="1.5" fill="rgba(255,255,255,0.4)"/>
  <circle cx="49" cy="15" r="1" fill="rgba(255,255,255,0.3)"/>
  <circle cx="15" cy="49" r="1" fill="rgba(255,255,255,0.3)"/>
  <circle cx="49" cy="49" r="1.5" fill="rgba(255,255,255,0.4)"/>
</svg>