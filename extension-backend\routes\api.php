<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\SiteController;
use App\Http\Controllers\Api\CookieFileController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PaymentLinkController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/login', [AuthController::class, 'login'])->name('api.auth.login');
Route::post('/auth/register', [AuthController::class, 'register']);
Route::get('/auth/check-extension-login', [AuthController::class, 'checkExtensionLogin']);

// Payment routes (public)
Route::post('/payment/create', [PaymentController::class, 'createPayment']);
Route::post('/payment/notification', [PaymentController::class, 'handleNotification']);
Route::get('/payment/status', [PaymentController::class, 'checkPaymentStatus']);

// Payment Link API routes
Route::post('/merchant-payment', [PaymentLinkController::class, 'createMerchantPaymentOrder']);
Route::get('/payment-link/{paymentLinkId}/status', [PaymentLinkController::class, 'getPaymentLinkStatus']);

// Subscription API routes
Route::post('/validate-discount', [App\Http\Controllers\SubscriptionController::class, 'validateDiscount']);
Route::post('/subscription/register', [App\Http\Controllers\SubscriptionController::class, 'processRegistration']);
Route::post('/subscription/notification', [App\Http\Controllers\SubscriptionController::class, 'handleMidtransNotification']);
Route::get('/subscription/payment-success', [App\Http\Controllers\SubscriptionController::class, 'handlePaymentSuccess']);
Route::post('/payment/success', [App\Http\Controllers\SubscriptionController::class, 'handlePaymentSuccess']);

// Protected routes
Route::middleware(['auth:sanctum', 'check.subscription'])->group(function () {
    // Auth routes
    Route::post('/auth/logout', [AuthController::class, 'logout'])->name('api.auth.logout');
    Route::get('/auth/me', [AuthController::class, 'me']);

    // Categories routes
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/categories/{identifier}', [CategoryController::class, 'show']);
    Route::get('/categories/{identifier}/sites', [CategoryController::class, 'getSites']);

    // Sites routes
    Route::get('/sites', [SiteController::class, 'index']);
    Route::post('/sites', [SiteController::class, 'store']);
    Route::get('/sites/{id}', [SiteController::class, 'show']);
    Route::get('/sites/{id}/cookies', [SiteController::class, 'getSiteCookies']);
    Route::post('/sites/cookies', [SiteController::class, 'getCookies']);

    // Cookie file management routes
    Route::post('/sites/{id}/cookie-file', [CookieFileController::class, 'uploadCookieFile']);
    Route::get('/sites/{id}/cookie-file', [CookieFileController::class, 'downloadCookieFile']);
    Route::delete('/sites/{id}/cookie-file', [CookieFileController::class, 'deleteCookieFile']);
});

// Default user route (optional)
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
