&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;id&quot;&gt;
&lt;head&gt;
  &lt;meta charset=&quot;UTF-8&quot;&gt;
  &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;&gt;
  &lt;title&gt;Contoh HTML untuk Encoder Tools&lt;/title&gt;
  &lt;style&gt;
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #0066cc;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 5px;
    }
    .feature-list {
      list-style-type: square;
    }
    .highlight {
      background-color: #ffffcc;
      padding: 2px 5px;
      border-radius: 3px;
    }
    code {
      font-family: monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
    }
    .special-chars {
      font-weight: bold;
      color: #cc0000;
    }
  &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;div class=&quot;container&quot;&gt;
    &lt;h1&gt;Encoder Tools&lt;/h1&gt;
    &lt;p&gt;Ini adalah contoh file HTML yang dapat digunakan untuk menguji &lt;span class=&quot;highlight&quot;&gt;Encoder Tools&lt;/span&gt;.&lt;/p&gt;
    
    &lt;h2&gt;Fitur-fitur:&lt;/h2&gt;
    &lt;ul class=&quot;feature-list&quot;&gt;
      &lt;li&gt;Encoding JSON&lt;/li&gt;
      &lt;li&gt;Encoding HTML&lt;/li&gt;
      &lt;li&gt;Encoding JavaScript&lt;/li&gt;
      &lt;li&gt;Encoding File&lt;/li&gt;
      &lt;li&gt;Encoding Folder&lt;/li&gt;
    &lt;/ul&gt;
    
    &lt;h2&gt;Contoh Penggunaan:&lt;/h2&gt;
    &lt;p&gt;Untuk menggunakan Encoder Tools, jalankan perintah berikut:&lt;/p&gt;
    &lt;pre&gt;&lt;code&gt;node index.js encode-html &quot;&amp;lt;div&amp;gt;Hello World&amp;lt;/div&amp;gt;&quot;&lt;/code&gt;&lt;/pre&gt;
    
    &lt;h2&gt;Karakter Khusus:&lt;/h2&gt;
    &lt;p class=&quot;special-chars&quot;&gt;!@#$%^&amp;*()_+{}[]|:;&quot;&#39;&lt;&gt;,.?/~`&lt;/p&gt;
    
    &lt;h2&gt;Contoh Tag HTML:&lt;/h2&gt;
    &lt;div&gt;
      &lt;p&gt;Paragraf &lt;strong&gt;tebal&lt;/strong&gt; dan &lt;em&gt;miring&lt;/em&gt;.&lt;/p&gt;
      &lt;a href=&quot;https://example.com&quot;&gt;Link ke example.com&lt;/a&gt;
      &lt;br&gt;
      &lt;img src=&quot;image.jpg&quot; alt=&quot;Gambar contoh&quot; width=&quot;100&quot;&gt;
      &lt;table border=&quot;1&quot;&gt;
        &lt;tr&gt;
          &lt;th&gt;Header 1&lt;/th&gt;
          &lt;th&gt;Header 2&lt;/th&gt;
        &lt;/tr&gt;
        &lt;tr&gt;
          &lt;td&gt;Data 1&lt;/td&gt;
          &lt;td&gt;Data 2&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/table&gt;
    &lt;/div&gt;
    
    &lt;h2&gt;Script Embedded:&lt;/h2&gt;
    &lt;script&gt;
      // Ini adalah contoh script JavaScript
      function showAlert() {
        alert(&quot;Hello from Encoder Tools!&quot;);
      }
      
      // Contoh objek JSON
      const config = {
        name: &quot;Encoder Tools&quot;,
        version: &quot;1.0.0&quot;,
        features: [&quot;JSON&quot;, &quot;HTML&quot;, &quot;JavaScript&quot;]
      };
      
      console.log(&quot;Config:&quot;, config);
    &lt;/script&gt;
  &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;