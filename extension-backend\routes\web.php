<?php

use App\Http\Controllers\Web\AuthController;
use Illuminate\Support\Facades\Route;

// Redirect root ke login
Route::get('/', function () {
    return view('welcome');
});

Route::get('/storage-link', function () {
    $targetFolder = base_path() . '/storage/app/public';
    $linkFolder =  $_SERVER['DOCUMENT_ROOT'] . '/storage';

    if (!file_exists($linkFolder)) {
        symlink($targetFolder, $linkFolder);
        return "Storage link successfully created!";
    } else {
        return "Storage link already exists!";
    }
});

// Routes untuk autentikasi pengguna elite/premium
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->name('login.post');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');

// Route untuk demo Payment Link
Route::get('/payment-link-demo', function () {
    return view('payment-link-demo');
})->name('payment-link-demo');

// Subscription routes
Route::get('/subscription/plans', [App\Http\Controllers\SubscriptionController::class, 'showPlans'])->name('subscription.plans');

// Payment demo route
Route::get('/payment/demo/{paymentLinkId}', function ($paymentLinkId) {
    // Demo data for payment page
    $orderData = [
        'order_id' => 'DEMO-' . time(),
        'gross_amount' => 49000
    ];

    $vaNumber = '8077' . rand(100000000000, 999999999999);

    return view('payment.demo', compact('orderData', 'vaNumber', 'paymentLinkId'));
})->name('payment.demo');

// Route untuk admin Filament (sudah ada secara otomatis di /admin)
