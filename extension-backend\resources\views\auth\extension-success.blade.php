<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Berhasil - <PERSON>tu <PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h2 class="text-xl font-semibold text-gray-900 mb-2">Login Berhasil!</h2>
            
            <p class="text-gray-600 mb-6">
                Selamat datang, <strong>{{ $user->name }}</strong>!<br>
                Anda dapat menutup tab ini dan kembali ke ekstensi browser.
            </p>
            
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                <div class="text-sm text-blue-700">
                    <p class="font-medium mb-2">Data Login Anda:</p>
                    <div class="text-left space-y-1">
                        <p><span class="font-medium">Nama:</span> {{ $user->name }}</p>
                        <p><span class="font-medium">Email:</span> {{ $user->email }}</p>
                        <p><span class="font-medium">Role:</span> 
                            <span class="px-2 py-1 text-xs font-medium rounded-full 
                                {{ $user->role === 'premium' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            
            <button onclick="window.close()" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200">
                Tutup Tab Ini
            </button>
            
            <p class="text-xs text-gray-500 mt-4">
                Jika tombol tidak berfungsi, silakan tutup tab ini secara manual.
            </p>
        </div>
    </div>
    
    <!-- Hidden data for extension -->
    <div id="extension-data" style="display: none;" 
         data-token="{{ $token }}" 
         data-user='{{ json_encode([
             "id" => $user->id,
             "name" => $user->name,
             "email" => $user->email,
             "role" => $user->role
         ]) }}'>
    </div>
    
    <script>
        // Get auth data
        const token = document.getElementById('extension-data').getAttribute('data-token');
        const userData = document.getElementById('extension-data').getAttribute('data-user');
        
        if (token && userData) {
            // Store in sessionStorage for extension to read
            sessionStorage.setItem('extension_auth_token', token);
            sessionStorage.setItem('extension_auth_user', userData);
            sessionStorage.setItem('extension_login_success', 'true');
            
            // Try to communicate with extension via custom event
            const event = new CustomEvent('extensionLoginSuccess', {
                detail: {
                    token: token,
                    user: JSON.parse(userData)
                }
            });
            window.dispatchEvent(event);
            
            // Also store in localStorage as fallback
            localStorage.setItem('extension_auth_token', token);
            localStorage.setItem('extension_auth_user', userData);
            
            // Send message to extension popup via postMessage
            try {
                const loginData = {
                    type: 'EXTENSION_LOGIN_SUCCESS',
                    token: token,
                    user: JSON.parse(userData)
                };
                
                // Send to parent window (if popup is opened in iframe)
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage(loginData, '*');
                }
                
                // Send to opener window (if popup was opened via window.open)
                if (window.opener) {
                    window.opener.postMessage(loginData, '*');
                }
                
                // Send to all windows (broadcast)
                window.postMessage(loginData, '*');
                
                console.log('Login success message sent to extension popup:', loginData);
            } catch (e) {
                console.log('Could not send postMessage to extension:', e);
            }
            
            // Notify extension background script about login success
            try {
                chrome.runtime.sendMessage('lhjmdmckadnioglkhlfhcegaboaefhah', {
                    action: 'loginSuccess',
                    token: token,
                    user: JSON.parse(userData)
                }, function(response) {
                    console.log('Login success notification sent to extension background');
                });
            } catch (e) {
                console.log('Could not send message to extension background:', e);
            }
        }
        
        // Auto close tab after 2 seconds
        setTimeout(function() {
            try {
                window.close();
            } catch (e) {
                console.log('Cannot auto-close tab');
            }
        }, 2000);
    </script>
</body>
</html>