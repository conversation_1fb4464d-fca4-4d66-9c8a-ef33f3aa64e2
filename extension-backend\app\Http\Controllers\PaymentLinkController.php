<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PaymentLinkController extends Controller
{
    public function __construct()
    {
        // Konfigurasi Midtrans
        \Midtrans\Config::$serverKey = config('midtrans.server_key');
        \Midtrans\Config::$isProduction = config('midtrans.is_production');
        \Midtrans\Config::$isSanitized = config('midtrans.is_sanitized');
        \Midtrans\Config::$is3ds = config('midtrans.is_3ds');
    }

    /**
     * Membuat Payment Link untuk merchant
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createMerchantPaymentOrder(Request $request): JsonResponse
    {
        try {
            // Validasi input
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|string|max:50',
                'gross_amount' => 'required|numeric|min:1000',
                'payment_link_id' => 'nullable|string|max:255',
                'customer_details' => 'required|array',
                'customer_details.first_name' => 'required|string|max:50',
                'customer_details.last_name' => 'nullable|string|max:50',
                'customer_details.email' => 'required|email|max:100',
                'customer_details.phone' => 'required|string|max:20',
                'item_details' => 'required|array|min:1',
                'item_details.*.id' => 'required|string',
                'item_details.*.name' => 'required|string',
                'item_details.*.price' => 'required|numeric',
                'item_details.*.quantity' => 'required|integer|min:1',
                'usage_limit' => 'nullable|integer|min:1|max:1000000',
                'expiry_duration' => 'nullable|integer|min:1',
                'expiry_unit' => 'nullable|string|in:minutes,hours,days',
                'enabled_payments' => 'nullable|array',
                'title' => 'nullable|string|max:40'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Cek konfigurasi Midtrans
            $serverKey = config('midtrans.server_key');
            if (empty($serverKey) || $serverKey === 'SB-Mid-server-YOUR_SERVER_KEY_HERE') {
                return $this->createDemoPaymentLink($request);
            }

            // Generate payment_link_id jika tidak disediakan
            $paymentLinkId = $request->input('payment_link_id') ?: 'merchant-payment-order-' . Str::random(10);

            // Siapkan parameter untuk Midtrans Payment Link API
            $params = [
                'transaction_details' => [
                    'order_id' => $request->input('order_id'),
                    'gross_amount' => (int) $request->input('gross_amount'),
                    'payment_link_id' => $paymentLinkId
                ],
                'customer_required' => true,
                'customer_details' => $request->input('customer_details'),
                'item_details' => $request->input('item_details')
            ];

            // Tambahkan parameter opsional jika ada
            if ($request->has('usage_limit')) {
                $params['usage_limit'] = (int) $request->input('usage_limit');
            }

            if ($request->has('expiry_duration') && $request->has('expiry_unit')) {
                $params['expiry'] = [
                    'duration' => (int) $request->input('expiry_duration'),
                    'unit' => $request->input('expiry_unit')
                ];
            }

            if ($request->has('enabled_payments')) {
                $params['enabled_payments'] = $request->input('enabled_payments');
            }

            if ($request->has('title')) {
                $params['title'] = $request->input('title');
            }

            // Panggil Midtrans Payment Link API
            try {
                $paymentLink = $this->callMidtransPaymentLinkAPI($params);

                Log::info('Payment Link created successfully', [
                    'order_id' => $request->input('order_id'),
                    'payment_url' => $paymentLink['payment_url'] ?? null
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Payment link created successfully',
                    'data' => $paymentLink
                ], 201);
            } catch (\Exception $e) {
                // Jika error 401 (unauthorized), fallback ke demo mode
                if (strpos($e->getMessage(), 'HTTP 401') !== false || strpos($e->getMessage(), 'unauthorized') !== false) {
                    Log::warning('Midtrans API unauthorized, falling back to demo mode', [
                        'order_id' => $request->input('order_id'),
                        'error' => $e->getMessage()
                    ]);
                    
                    return $this->createDemoPaymentLink($request);
                }
                
                // Untuk error lainnya, throw exception
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Payment Link creation failed', [
                'error' => $e->getMessage(),
                'order_id' => $request->input('order_id')
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Payment link creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Panggil Midtrans Payment Link API
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function callMidtransPaymentLinkAPI(array $params): array
    {
        $serverKey = config('midtrans.server_key');
        $isProduction = config('midtrans.is_production');

        // Tentukan base URL berdasarkan environment
        $baseUrl = $isProduction ? 'https://api.midtrans.com' : 'https://api.sandbox.midtrans.com';
        $url = $baseUrl . '/v1/payment-links';

        // Siapkan headers
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($serverKey . ':')
        ];

        // Inisialisasi cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($params),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            throw new \Exception('cURL Error: ' . $curlError);
        }

        $responseData = json_decode($response, true);

        if ($httpCode !== 201) {
            $errorMessage = $responseData['error_messages'][0] ?? 'Unknown error occurred';
            throw new \Exception('Midtrans API Error (HTTP ' . $httpCode . '): ' . $errorMessage);
        }

        return $responseData;
    }

    /**
     * Buat demo payment link untuk testing
     *
     * @param Request $request
     * @return JsonResponse
     */
    private function createDemoPaymentLink(Request $request): JsonResponse
    {
        $paymentLinkId = $request->input('payment_link_id') ?: 'merchant-payment-order-' . Str::random(10);
        $orderData = $request->all();

        // Generate demo payment information
        $paymentMethods = [
            'bank_transfer' => [
                'bca' => ['account' => '**********', 'name' => 'PT Satu Pintu'],
                'bni' => ['account' => '**********', 'name' => 'PT Satu Pintu'],
                'bri' => ['account' => '**********', 'name' => 'PT Satu Pintu'],
                'mandiri' => ['account' => '**********', 'name' => 'PT Satu Pintu']
            ],
            'qris' => [
                'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                'amount' => $orderData['gross_amount']
            ],
            'virtual_account' => [
                'va_number' => '8077' . rand(************, ************),
                'bank' => 'bca'
            ]
        ];

        $demoResponse = [
            'payment_url' => url('/payment/demo/' . $paymentLinkId),
            'payment_link_id' => $paymentLinkId,
            'order_id' => $orderData['order_id'],
            'gross_amount' => $orderData['gross_amount'],
            'status' => 'pending',
            'created_at' => now()->toISOString(),
            'expires_at' => now()->addHours($orderData['expiry_duration'] ?? 24)->toISOString(),
            'payment_methods' => $paymentMethods,
            'demo_mode' => true,
            'instructions' => [
                'Ini adalah mode demo pembayaran',
                'Gunakan informasi pembayaran di atas untuk simulasi',
                'Pembayaran akan otomatis berhasil setelah 5 menit',
                'Akun akan dibuat otomatis setelah pembayaran berhasil'
            ]
        ];

        Log::info('Demo Payment Link created', [
            'order_id' => $request->input('order_id'),
            'payment_url' => $demoResponse['payment_url']
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Payment link created successfully (Demo Mode)',
            'data' => $demoResponse
        ], 201);
    }

    /**
     * Get payment link status
     */
    public function getPaymentLinkStatus($paymentLinkId)
    {
        try {
            $serverKey = config('midtrans.server_key');

            // Jika server key tidak valid, return demo status
            if (empty($serverKey) || $serverKey === 'SB-Mid-server-YOUR_SERVER_KEY_HERE') {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Payment link status retrieved successfully (Demo Mode)',
                    'data' => [
                        'payment_link_id' => $paymentLinkId,
                        'status' => 'pending',
                        'usage_count' => 0,
                        'usage_limit' => 1,
                        'created_at' => now()->subHours(1)->toISOString(),
                        'expires_at' => now()->addHours(23)->toISOString(),
                        'demo_mode' => true
                    ]
                ]);
            }

            // Call Midtrans API untuk mendapatkan status
            $url = config('midtrans.is_production')
                ? "https://api.midtrans.com/v1/payment-links/{$paymentLinkId}"
                : "https://api.sandbox.midtrans.com/v1/payment-links/{$paymentLinkId}";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Authorization: Basic ' . base64_encode($serverKey . ':')
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                return response()->json([
                    'status' => 'success',
                    'message' => 'Payment link status retrieved successfully',
                    'data' => $data
                ]);
            } else {
                $errorData = json_decode($response, true);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to get payment link status',
                    'error' => $errorData['error_messages'] ?? ['Unknown error occurred']
                ], $httpCode);
            }

        } catch (Exception $e) {
            Log::error('Payment link status check failed: ' . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'Payment link status check failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
