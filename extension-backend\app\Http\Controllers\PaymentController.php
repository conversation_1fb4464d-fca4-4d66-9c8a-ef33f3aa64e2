<?php

namespace App\Http\Controllers;

use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Midtrans\Config;
use Midtrans\Snap;
use Midtrans\Notification;

class PaymentController extends Controller
{
    public function __construct()
    {
        // Set Midtrans configuration
        Config::$serverKey = config('midtrans.server_key');
        Config::$isProduction = config('midtrans.is_production');
        Config::$isSanitized = config('midtrans.is_sanitized');
        Config::$is3ds = config('midtrans.is_3ds');
    }

    public function createPayment(Request $request)
    {
        try {
            $request->validate([
                'role' => 'required|in:elite,premium',
                'amount' => 'required|numeric',
                'customer_details' => 'required|array',
                'customer_details.first_name' => 'required|string',
                'customer_details.email' => 'required|email'
            ]);

            $role = $request->role;
            $amount = $request->amount;
            $customerDetails = $request->customer_details;
            $orderId = 'ORDER-' . time() . '-' . uniqid();

            // Cek konfigurasi Midtrans
            $serverKey = config('midtrans.server_key');
            if (empty($serverKey) || $serverKey === 'SB-Mid-server-YOUR_SERVER_KEY_HERE') {
                // Mode demo - return mock response
                Log::info('Demo mode: Using mock payment response');
                return response()->json([
                    'success' => true,
                    'snap_token' => 'demo_token_' . $orderId,
                    'order_id' => $orderId,
                    'amount' => $amount,
                    'message' => 'Demo mode - payment simulation'
                ]);
            }

            // Cek apakah user sudah ada
            $user = Anyone::where('email', $customerDetails['email'])->first();
            if (!$user) {
                // Buat user baru dengan role 'none'
                $user = Anyone::create([
                    'name' => $customerDetails['first_name'],
                    'email' => $customerDetails['email'],
                    'password' => bcrypt('default123'), // Password default
                    'role' => 'none',
                    'payment_status' => 'pending'
                ]);
            }

            // Update payment info
            $user->update([
                'midtrans_order_id' => $orderId,
                'payment_amount' => $amount,
                'payment_status' => 'pending'
            ]);

            // Parameter untuk Midtrans
            $params = [
                'transaction_details' => [
                    'order_id' => $orderId,
                    'gross_amount' => $amount,
                ],
                'customer_details' => [
                    'first_name' => $customerDetails['first_name'],
                    'email' => $customerDetails['email'],
                ],
                'item_details' => [[
                    'id' => $role,
                    'price' => $amount,
                    'quantity' => 1,
                    'name' => 'Satu Pintu ' . ucfirst($role) . ' Plan'
                ]]
            ];

            $snapToken = Snap::getSnapToken($params);

            return response()->json([
                'success' => true,
                'snap_token' => $snapToken,
                'order_id' => $orderId,
                'amount' => $amount
            ]);

        } catch (\Exception $e) {
            Log::error('Payment creation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat pembayaran: ' . $e->getMessage()
            ], 500);
        }
    }

    public function handleNotification(Request $request)
    {
        try {
            $notification = new Notification();
            
            $orderId = $notification->order_id;
            $transactionStatus = $notification->transaction_status;
            $fraudStatus = $notification->fraud_status;
            $transactionId = $notification->transaction_id;

            Log::info('Midtrans notification received', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'fraud_status' => $fraudStatus
            ]);

            // Cari user berdasarkan order_id
            $user = Anyone::where('midtrans_order_id', $orderId)->first();
            
            if (!$user) {
                Log::error('User not found for order_id: ' . $orderId);
                return response()->json(['message' => 'User not found'], 404);
            }

            // Update transaction_id
            $user->midtrans_transaction_id = $transactionId;
            $user->midtrans_response = json_encode($notification->getResponse());

            if ($transactionStatus == 'capture') {
                if ($fraudStatus == 'challenge') {
                    $user->payment_status = 'pending';
                } else if ($fraudStatus == 'accept') {
                    $user->payment_status = 'paid';
                    $user->payment_date = now();
                    $this->upgradeUserRole($user);
                }
            } else if ($transactionStatus == 'settlement') {
                $user->payment_status = 'paid';
                $user->payment_date = now();
                $this->upgradeUserRole($user);
            } else if ($transactionStatus == 'pending') {
                $user->payment_status = 'pending';
            } else if ($transactionStatus == 'deny' || $transactionStatus == 'expire' || $transactionStatus == 'cancel') {
                $user->payment_status = 'failed';
            }

            $user->save();

            return response()->json(['message' => 'Notification handled successfully']);

        } catch (\Exception $e) {
            Log::error('Notification handling failed: ' . $e->getMessage());
            return response()->json(['message' => 'Notification handling failed'], 500);
        }
    }

    private function upgradeUserRole($user)
    {
        // Tentukan role berdasarkan payment amount
        if ($user->payment_amount == 29000) {
            $user->role = 'elite';
        } else if ($user->payment_amount == 49000) {
            $user->role = 'premium';
        }
        
        $user->is_active = true;
        Log::info('User role upgraded', [
            'user_id' => $user->id,
            'email' => $user->email,
            'new_role' => $user->role
        ]);
    }

    public function checkPaymentStatus(Request $request)
    {
        try {
            $request->validate([
                'order_id' => 'required|string'
            ]);

            $user = Anyone::where('midtrans_order_id', $request->order_id)->first();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order tidak ditemukan'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'payment_status' => $user->payment_status,
                'role' => $user->role,
                'payment_date' => $user->payment_date
            ]);

        } catch (\Exception $e) {
            Log::error('Check payment status failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengecek status pembayaran'
            ], 500);
        }
    }
}