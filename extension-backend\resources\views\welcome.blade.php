<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <!-- CSRF Token Meta -->
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>Satu Pintu - Home</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <!-- Midtrans Snap -->
        <script type="text/javascript" src="https://app.sandbox.midtrans.com/snap/snap.js" data-client-key="{{ config('midtrans.client_key') }}"></script>

        <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
        <!-- Navigation -->
        <nav class="bg-black/20 backdrop-blur-sm border-b border-white/10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-3">
                        <!-- Logo Satu Pintu -->
                        <svg width="40" height="40" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                              <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                              <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="numberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                              <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                            </linearGradient>
                            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                              <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
                            </filter>
                          </defs>

                          <!-- Background Circle -->
                          <circle cx="32" cy="32" r="30" fill="url(#doorGradient)" filter="url(#shadow)"/>

                          <!-- Door Frame -->
                          <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="#ffffff" opacity="0.9"/>
                          <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradient)"/>

                          <!-- Door Handle -->
                          <circle cx="38" cy="32" r="2" fill="#fbbf24"/>
                          <circle cx="38" cy="32" r="1.5" fill="#f59e0b"/>

                          <!-- Door Panel Lines -->
                          <rect x="22" y="18" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                          <rect x="22" y="22" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                          <rect x="22" y="40" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                          <rect x="22" y="44" width="20" height="1" fill="rgba(255,255,255,0.3)"/>

                          <!-- Number "1" -->
                          <g transform="translate(26, 26)">
                            <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18"
                                  stroke="url(#numberGradient)"
                                  stroke-width="3"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  fill="none"/>
                            <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18"
                                  stroke="#ffffff"
                                  stroke-width="1.5"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  fill="none"/>
                          </g>

                          <!-- Decorative Elements -->
                          <circle cx="15" cy="15" r="1.5" fill="rgba(255,255,255,0.4)"/>
                          <circle cx="49" cy="15" r="1" fill="rgba(255,255,255,0.3)"/>
                          <circle cx="15" cy="49" r="1" fill="rgba(255,255,255,0.3)"/>
                          <circle cx="49" cy="49" r="1.5" fill="rgba(255,255,255,0.4)"/>
                        </svg>
                        <h1 class="text-xl font-bold">🚪 Satu Pintu</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        @if (Route::has('login'))
                            @auth
                                <a href="{{ url('/dashboard') }}" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                                    Dashboard
                                </a>
                            @else
                                <a href="{{ route('login') }}" class="text-white/80 hover:text-white transition-colors">
                                    Masuk
                                </a>
                                @if (Route::has('register'))
                                    <a href="{{ route('register') }}" class="bg-white/10 hover:bg-white/20 px-4 py-2 rounded-lg transition-colors">
                                        Daftar
                                    </a>
                                @endif
                            @endauth
                        @endif
                    </div>
                </div>
            </div>
        </nav>
        <!-- Hero Section -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    Selamat Datang di <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">Satu Pintu</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto">
                    Solusi terdepan untuk manajemen ekstensi browser yang aman dan efisien. Tingkatkan pengalaman browsing Anda dengan fitur premium kami.
                </p>
            </div>

            <!-- Features Section -->
            <div class="grid md:grid-cols-3 gap-8 mb-16">
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Keamanan Tinggi</h3>
                    <p class="text-white/70">Perlindungan data dan privasi dengan enkripsi tingkat enterprise</p>
                </div>

                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Performa Optimal</h3>
                    <p class="text-white/70">Optimasi kecepatan dan efisiensi untuk pengalaman browsing terbaik</p>
                </div>

                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Mudah Digunakan</h3>
                    <p class="text-white/70">Interface intuitif yang mudah dipahami untuk semua kalangan</p>
                </div>
            </div>

            <!-- Pricing Section -->
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold mb-4">Pilih Paket Premium Anda</h2>
                <p class="text-xl text-white/80">Upgrade ke fitur premium untuk pengalaman yang lebih lengkap</p>
                
                <!-- Demo Mode Notice -->
                <div class="mt-6 mx-auto max-w-2xl">
                    <div class="bg-yellow-500/20 border border-yellow-400/50 rounded-lg p-4">
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-yellow-400 font-semibold">Mode Demo Aktif</span>
                        </div>
                        <p class="text-yellow-200 text-sm mt-2">
                            Sistem pembayaran sedang dalam mode demo. Klik tombol pembayaran untuk melihat simulasi proses pembayaran.
                        </p>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Elite Package -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-yellow-400/50 transition-all duration-300 transform hover:scale-105">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-2">Elite</h3>
                        <div class="text-4xl font-bold mb-6">
                            <span class="text-yellow-400">Rp 29.000</span>
                        </div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Akses fitur dasar premium
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Sinkronisasi multi-device
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Support email 24/7
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Backup otomatis
                            </li>
                        </ul>
                        <a href="{{ route('subscription.plans') }}" class="block w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-center">
                            Pilih Elite
                        </a>
                    </div>
                </div>

                <!-- Premium Package -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border-2 border-purple-400 hover:border-purple-300 transition-all duration-300 transform hover:scale-105 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                            TERPOPULER
                        </span>
                    </div>
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-2">Premium</h3>
                        <div class="text-4xl font-bold mb-6">
                            <span class="text-purple-400">Rp 49.000</span>
                        </div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Semua fitur Elite
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Fitur AI advanced
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Analytics mendalam
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Priority support
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Custom integrations
                            </li>
                        </ul>
                        <a href="{{ route('subscription.plans') }}" class="block w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-center">
                            Pilih Premium
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- JavaScript untuk Midtrans -->
        <script>
            function payWithMidtrans(role, amount) {
                // Kirim request ke backend untuk membuat transaksi
                fetch('{{ url("/api/payment/create") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        role: role,
                        amount: amount,
                        customer_details: {
                            first_name: 'User',
                            email: '<EMAIL>'
                        }
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Cek apakah ini mode demo
                        if (data.snap_token && data.snap_token.startsWith('demo_token_')) {
                            // Mode demo - simulasi pembayaran
                            alert('🎯 Mode Demo Aktif!\n\nSimulasi pembayaran berhasil untuk paket ' + role.toUpperCase() + ' (Rp ' + amount.toLocaleString('id-ID') + ').\n\nUntuk menggunakan pembayaran sesungguhnya, silakan konfigurasi Midtrans API key yang valid di file .env');
                            console.log('Demo payment simulation:', data);
                        } else {
                            // Mode produksi - buka Midtrans Snap
                            snap.pay(data.snap_token, {
                                onSuccess: function(result) {
                                    alert('Pembayaran berhasil!');
                                    console.log(result);
                                    // Redirect ke halaman sukses atau refresh
                                    window.location.reload();
                                },
                                onPending: function(result) {
                                    alert('Pembayaran pending, silakan selesaikan pembayaran.');
                                    console.log(result);
                                },
                                onError: function(result) {
                                    alert('Pembayaran gagal!');
                                    console.log(result);
                                }
                            });
                        }
                    } else {
                        alert('Gagal membuat transaksi: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memproses pembayaran: ' + error.message);
                });
            }
        </script>
    </body>
</html>
