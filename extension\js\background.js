// Background script untuk Cookie Manager Extension

// Constants for local storage
const CACHE_KEYS = {
  USER_SESSION: 'satuPintu_user_session',
  SESSIONS_FOLDER: 'satuPintu_sessions',
  SITES_DATA: 'satuPintu_sites',
  LAST_SYNC: 'satuPintu_last_sync'
};

const SESSION_STRUCTURE = {
  USER_FILE: 'user.json',
  COOKIES_FOLDER: 'cookies'
};

const API_BASE_URL = 'http://127.0.0.1:8000/api';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Track login processing to prevent duplicates
let isProcessingLogin = false;
let lastLoginProcessTime = 0;
let isProcessingCookies = false;
let cookieOperationTimeout = null;
let lastAutoInjectDomains = new Map(); // Track last auto-inject time per domain

// Global function to reset cookie processing flag with debouncing
function resetFlag() {
  if (cookieOperationTimeout) {
    clearTimeout(cookieOperationTimeout);
  }
  cookieOperationTimeout = setTimeout(() => {
    isProcessingCookies = false;
    console.log('🔓 Cookie processing flag cleared - listener active again');
  }, 2000); // Wait 2 seconds for better debouncing
}

// Enhanced flag management for auto-inject
function setAutoInjectFlag() {
  isProcessingCookies = true;
  console.log('🔒 Auto-inject flag set - preventing cookie loops');
  
  if (cookieOperationTimeout) {
    clearTimeout(cookieOperationTimeout);
  }
  
  // Longer timeout for auto-inject to prevent rapid re-triggering
  cookieOperationTimeout = setTimeout(() => {
    isProcessingCookies = false;
    console.log('🔓 Auto-inject flag cleared - operations allowed');
  }, 15000); // 15 seconds for auto-inject
}

// Event listener untuk instalasi extension
chrome.runtime.onInstalled.addListener(() => {
  console.log("🚀 Cookie Manager Extension installed");
  console.log("🔧 COOKIE LOOPING PREVENTION ACTIVE:");
  console.log("   - Auto-inject cookies: ENABLED with flag protection");
  console.log("   - Cookie listener: ENABLED with flag protection");
  console.log("   - Manual cookie import: ENABLED (click sites to import)");
  console.log("   - Cookie operations: Protected with flags");
  // Initialize cache on install
  initializeCache();
});

// Event listener untuk pesan dari popup atau content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    console.log('Background script received message:', request.action, request);
    
    if (request.action === "loginSuccess") {
      console.log('Processing login success with authData:', request.authData);
      console.log('Message source:', request.source || 'unknown');
      console.log('Message sender:', sender);
      
      // Validate authData
      if (!request.authData) {
        console.error('authData is missing from loginSuccess message');
        sendResponse({ success: false, error: 'authData is missing' });
        return;
      }
      
      // Prevent duplicate processing
      const currentTime = Date.now();
      if (isProcessingLogin && (currentTime - lastLoginProcessTime) < 5000) {
        console.log('Login already being processed, ignoring duplicate request');
        sendResponse({ success: true, message: 'Already processing' });
        return;
      }
      
      isProcessingLogin = true;
      lastLoginProcessTime = currentTime;
      
      // Set badge to indicate login success
      chrome.action.setBadgeText({ text: "✓" });
      chrome.action.setBadgeBackgroundColor({ color: "#10B981" });
      
      // Download and cache user data
      downloadAndCacheUserData(request.authData)
          .then(async () => {
            // Create notification without icon to avoid download errors
            try {
              await chrome.notifications.create({
                type: 'basic',
                title: 'Satu Pintu - Login Berhasil',
                message: 'Data berhasil disinkronkan. Klik ikon ekstensi untuk mengakses situs Anda'
              });
              console.log('Login notification created successfully');
            } catch (error) {
              console.log('Notification creation failed:', error);
            }
          
          // Notify popup about successful login (only if popup is open)
          try {
            await chrome.runtime.sendMessage({
              action: 'loginSuccessFromBackground',
              authData: request.authData
            });
            console.log('Login success message sent to popup');
          } catch (error) {
            console.log('Could not send message to popup (popup may be closed):', error);
            // This is normal if popup is closed, no need to treat as error
          }
          
          isProcessingLogin = false; // Reset flag
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('Error caching user data:', error);
          isProcessingLogin = false; // Reset flag
          sendResponse({ success: false, error: error.message });
        });
      
      return true;
    }

    if (request.action === "logout") {
      // Clear all cached data on logout
      clearAllCachedData()
        .then(() => {
          chrome.action.setBadgeText({ text: "" });
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('Error clearing cached data:', error);
          sendResponse({ success: false, error: error.message });
        });
      
      return true;
    }

    if (request.action === "getCachedData") {
      // Return cached data if available and fresh
      getCachedUserData()
        .then((cachedData) => {
          if (cachedData) {
            sendResponse({
              success: true,
              userSession: cachedData.userSession,
              sitesData: cachedData.sites,
              sessionsFolder: cachedData.sessionsFolder,
              cookiesFolder: cachedData.cookies,
              lastSync: cachedData.lastSync
            });
          } else {
            sendResponse({ success: false, message: 'No cached data found' });
          }
        })
        .catch((error) => {
          console.error('Error getting cached data:', error);
          sendResponse({ success: false, error: error.message });
        });
      
      return true;
    }

    if (request.action === "getCachedCookiesForSite") {
      getCachedCookiesForSite(request.siteId)
        .then((cookiesData) => {
          sendResponse(cookiesData);
        })
        .catch((error) => {
          console.error('Error getting cached cookies for site:', error);
          sendResponse({ success: false, error: error.message });
        });
      
      return true;
    }

    if (request.action === "setCookie") {
      console.log('Received setCookie request:', request.cookie);
      setCookieBackground(request.cookie)
        .then((result) => {
          console.log('setCookie success:', result);
          sendResponse({ success: true, result });
        })
        .catch((error) => {
          console.error('setCookie error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Indicates we will send a response asynchronously
    }

    if (request.action === "setCookies") {
      console.log('Received setCookies request for', request.cookies?.length || 0, 'cookies');
      setCookiesBackground(request.cookies, request.domain)
        .then((result) => {
          console.log('setCookies success:', result);
          sendResponse({ success: true, result });
        })
        .catch((error) => {
          console.error('setCookies error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }

    if (request.action === "getCookies") {
      getCookiesBackground(request.domain)
        .then((cookies) => sendResponse({ success: true, cookies }))
        .catch((error) => {
          console.error('getCookies error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }

    if (request.action === "clearCookies") {
      clearCookiesBackground(request.domain)
        .then((result) => sendResponse({ success: true, result }))
        .catch((error) => {
          console.error('clearCookies error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true;
    }

    if (request.action === "getAllCookiesForDomain") {
      getAllCookiesForDomainBackground(request.domain)
        .then((cookies) => sendResponse({ success: true, cookies }))
        .catch((error) => {
          console.error('getAllCookiesForDomain error:', error);
          sendResponse({ success: false, error: error.message, cookies: [] });
        });
      return true;
    }

    if (request.action === "cookieChanged") {
      // Handle cookie change notifications from content script
      return true;
    }
  } catch (error) {
    console.error('Runtime onMessage error:', error);
    sendResponse({ success: false, error: 'Background script error', cookies: [] });
  }
});

// Fungsi untuk mengatur cookie menggunakan Chrome API
async function setCookieBackground(cookieData) {
  return new Promise((resolve, reject) => {
    // Set flag to prevent cookie listener from processing our changes
    isProcessingCookies = true;
    console.log('🔒 Cookie processing flag set - ignoring cookie listener');
    
    // Clear any existing timeout
    if (cookieOperationTimeout) {
      clearTimeout(cookieOperationTimeout);
    }
    
    // Reset flag will be handled by global resetFlag() function
    // Validate required fields
    if (!cookieData.name || cookieData.value === undefined) {
      resetFlag();
      reject(new Error('Cookie name and value are required'));
      return;
    }

    // Handle special cookie prefixes
    const isHostPrefixed = cookieData.name.startsWith("__Host-");
    const isSecurePrefixed = cookieData.name.startsWith("__Secure-");

    // Clean domain (remove protocol if present)
    let cleanDomain = cookieData.domain;
    if (cleanDomain) {
      cleanDomain = cleanDomain.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
      // Keep the leading dot for domain cookies, but clean for URL construction
    } else {
      console.warn(`Cookie ${cookieData.name} has no domain specified`);
      resetFlag();
      reject(new Error(`Cookie ${cookieData.name} requires a domain`));
      return;
    }
    
    // Get clean domain for URL (without leading dot)
    const urlDomain = cleanDomain.startsWith('.') ? cleanDomain.substring(1) : cleanDomain;

    // Determine protocol based on secure flag
    const protocol = (cookieData.secure !== false || isHostPrefixed || isSecurePrefixed) ? 'https' : 'http';
    
    // Determine URL
    let url;
    if (isHostPrefixed) {
      url = `${protocol}://${urlDomain}/`;
    } else {
      url = `${protocol}://${urlDomain}${cookieData.path || "/"}`;
    }

    const cookieDetails = {
      url: url,
      name: cookieData.name,
      value: cookieData.value,
    };

    // Handle domain
    if (!isHostPrefixed && cookieData.domain) {
      if (cookieData.domain.startsWith('.')) {
        cookieDetails.domain = cookieData.domain;
      } else if (cookieData.domain !== urlDomain) {
        cookieDetails.domain = `.${urlDomain}`;
      } else {
        cookieDetails.domain = cookieData.domain;
      }
    }

    // Handle path
    if (cookieData.path) {
      cookieDetails.path = cookieData.path;
    } else if (isHostPrefixed) {
      cookieDetails.path = "/";
    } else {
      cookieDetails.path = "/";
    }

    // Security attributes
    if (cookieData.secure !== undefined) {
      cookieDetails.secure = cookieData.secure;
    } else if (isHostPrefixed || isSecurePrefixed) {
      cookieDetails.secure = true;
    } else {
      cookieDetails.secure = protocol === 'https';
    }

    if (cookieData.httpOnly !== undefined) {
      cookieDetails.httpOnly = cookieData.httpOnly;
    }

    // Handle sameSite with validation
    if (cookieData.sameSite) {
      const validSameSite = ["unspecified", "no_restriction", "lax", "strict"];
      if (validSameSite.includes(cookieData.sameSite)) {
        cookieDetails.sameSite = cookieData.sameSite;
      } else if (cookieData.sameSite === "none") {
        cookieDetails.sameSite = "no_restriction";
      } else {
        cookieDetails.sameSite = "lax"; // default fallback
      }
    }

    // Handle expiration
    if (cookieData.expirationDate) {
      if (typeof cookieData.expirationDate === 'number') {
        cookieDetails.expirationDate = cookieData.expirationDate;
      } else if (cookieData.expirationDate instanceof Date) {
        cookieDetails.expirationDate = Math.floor(cookieData.expirationDate.getTime() / 1000);
      }
    } else if (cookieData.expires) {
      const expiry =
        typeof cookieData.expires === "number"
          ? cookieData.expires > 1000000000000
            ? cookieData.expires / 1000
            : cookieData.expires
          : new Date(cookieData.expires).getTime() / 1000;
      cookieDetails.expirationDate = expiry;
    } else {
      // Set expiration to 1 year from now if not specified
      cookieDetails.expirationDate = Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60);
    }

    console.log("Setting cookie:", cookieData.name);
    console.log("Cookie details:", {
      url: cookieDetails.url,
      name: cookieDetails.name,
      domain: cookieDetails.domain,
      path: cookieDetails.path,
      secure: cookieDetails.secure,
      httpOnly: cookieDetails.httpOnly,
      sameSite: cookieDetails.sameSite,
      expirationDate: cookieDetails.expirationDate
    });

    // Function to attempt setting cookie with retry mechanism
    const attemptSetCookie = (details, isRetry = false) => {
      chrome.cookies.set(details, (result) => {
        if (chrome.runtime.lastError) {
          console.error(`Failed to set cookie ${cookieData.name}:`, chrome.runtime.lastError);
          console.error('Cookie details:', details);
          
          // If HTTPS failed and this is not a retry, try with HTTP
          if (!isRetry && details.url.startsWith('https://')) {
            console.log(`Retrying cookie ${cookieData.name} with HTTP...`);
            const httpDetails = { ...details };
            httpDetails.url = details.url.replace('https://', 'http://');
            httpDetails.secure = false;
            attemptSetCookie(httpDetails, true);
          } else {
            resetFlag();
            reject(
              new Error(
                `Failed to set cookie "${cookieData.name}": ${chrome.runtime.lastError.message}`
              )
            );
          }
        } else {
          console.log(`Cookie ${cookieData.name} set successfully:`, result);
          resetFlag();
          resolve(result);
        }
      });
    };

    // Start the attempt
    attemptSetCookie(cookieDetails);
  });
}

// Fungsi untuk mengatur multiple cookies sekaligus
async function setCookiesBackground(cookies, domain) {
  if (!cookies || !Array.isArray(cookies)) {
    throw new Error('Cookies must be an array');
  }

  // Set flag to prevent cookie listener from processing our changes
  isProcessingCookies = true;
  console.log('🔒 Multiple cookies processing flag set - ignoring cookie listener');
  
  // Clear any existing timeout
  if (cookieOperationTimeout) {
    clearTimeout(cookieOperationTimeout);
  }
  
  // Reset flag will be handled by global resetFlag() function

  console.log(`Setting ${cookies.length} cookies for domain: ${domain}`);
  
  try {
    const results = {
      total: cookies.length,
      success: 0,
      failed: 0,
      errors: []
    };

    // Set cookies sequentially to avoid overwhelming the API
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      console.log(`Processing cookie ${i + 1}/${cookies.length}: ${cookie.name}`);
      
      try {
        // Ensure domain is set if not present
        if (!cookie.domain && domain) {
          cookie.domain = domain;
        }
        
        await setCookieBackground(cookie);
        results.success++;
        console.log(`✓ Cookie ${cookie.name} set successfully`);
        
        // Small delay between cookies to prevent race conditions
        if (i < cookies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        results.failed++;
        results.errors.push({
          cookieName: cookie.name,
          error: error.message
        });
        console.error(`✗ Failed to set cookie ${cookie.name}:`, error.message);
      }
    }

    console.log(`Cookies setting completed: ${results.success}/${results.total} successful`);
    
    if (results.failed > 0) {
      console.warn(`${results.failed} cookies failed to set:`, results.errors);
    }

    // Reset flag after all cookies are processed
    resetFlag();
    
    return results;
  } catch (error) {
    console.error('Error in setCookiesBackground:', error);
    resetFlag();
    throw error;
  }
}

// Fungsi untuk mendapatkan cookies dari domain tertentu
async function getCookiesBackground(domain) {
  return new Promise((resolve, reject) => {
    const url = `https://${domain}`;

    chrome.cookies.getAll({ domain: domain }, (cookies) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(cookies);
      }
    });
  });
}

// Fungsi untuk mendapatkan SEMUA cookies untuk domain tertentu (termasuk subdomain)
async function getAllCookiesForDomainBackground(domain) {
  return new Promise((resolve, reject) => {
    try {
      // Dapatkan cookies untuk domain utama
      chrome.cookies.getAll({ domain: domain }, (mainCookies) => {
           if (chrome.runtime.lastError) {
             reject(new Error(chrome.runtime.lastError.message));
             return;
           }
 
           // Dapatkan cookies untuk domain dengan prefix "." (subdomain)
           chrome.cookies.getAll({ domain: `.${domain}` }, (subCookies) => {
             if (chrome.runtime.lastError) {
               // Jika gagal mendapatkan subdomain cookies, tetap lanjutkan dengan main cookies
               resolve(mainCookies || []);
               return;
             }

          // Gabungkan dan deduplikasi cookies
          const allCookies = [...(mainCookies || []), ...(subCookies || [])];
          const uniqueCookies = allCookies.filter((cookie, index, self) => 
            index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
          );



          resolve(uniqueCookies);
        });
      });
    } catch (error) {
       reject(error);
     }
  });
}

// Fungsi untuk menghapus cookies dari domain tertentu
async function clearCookiesBackground(domain) {
  return new Promise(async (resolve, reject) => {
    // DISABLED: Cookie clearing to prevent looping
    // Instead of clearing, we'll overwrite cookies directly
    console.log(`🔄 Skipping cookie clearing for domain: ${domain} - using overwrite strategy`);
    
    try {
      // Just return success without actually clearing
      // This prevents the cookie looping issue
      resolve({ deletedCount: 0, message: 'Clearing skipped - using overwrite strategy' });
    } catch (error) {
      console.error('Error in clearCookiesBackground:', error);
      reject(error);
    }
  });
}

// Event listener untuk perubahan tab - AUTO-INJECT ENABLED dengan perlindungan flag
// Auto-inject diaktifkan dengan perlindungan isProcessingCookies untuk mencegah looping
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url && isValidUrl(tab.url)) {
    console.log("🔄 Tab updated:", tab.url);
    
    // Skip auto-inject jika sedang memproses cookies untuk mencegah looping
    if (isProcessingCookies) {
      console.log("⏸️ Auto-inject skipped - cookie operation in progress");
      return;
    }
    
    try {
      // Extract domain from URL
      const url = new URL(tab.url);
      const domain = url.hostname;
      
      // Check if we have stored auth data
      const authData = await getStoredAuthData();
      if (authData && authData.token) {
        console.log("🚀 Auto-injecting cookies for domain:", domain);
        // Try to get cookies for this domain from backend
        await autoInjectCookiesForDomain(domain, authData.token, tabId);
      } else {
        console.log("ℹ️ No auth data found for auto-inject");
      }
    } catch (error) {
      console.log('❌ Auto-inject cookies error:', error.message);
    }
  }
});

// Function to get stored auth data
async function getStoredAuthData() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['satuPintuAuth'], (result) => {
      if (chrome.runtime.lastError) {
        console.error('Error getting stored auth:', chrome.runtime.lastError);
        resolve(null);
      } else {
        resolve(result.satuPintuAuth || null);
      }
    });
  });
}

// Function to auto-inject cookies for a domain
async function autoInjectCookiesForDomain(domain, token, tabId) {
  // Check if we recently auto-injected for this domain
  const now = Date.now();
  const lastInjectTime = lastAutoInjectDomains.get(domain);
  const DOMAIN_COOLDOWN = 30000; // 30 seconds cooldown per domain
  
  if (lastInjectTime && (now - lastInjectTime) < DOMAIN_COOLDOWN) {
    console.log(`⏰ Auto-inject skipped for ${domain} - cooldown active (${Math.round((DOMAIN_COOLDOWN - (now - lastInjectTime)) / 1000)}s remaining)`);
    return;
  }
  
  // Set enhanced flag untuk mencegah looping
  setAutoInjectFlag();
  
  // Record this auto-inject attempt
  lastAutoInjectDomains.set(domain, now);
  
  try {
    console.log(`🎯 Attempting auto-inject for domain: ${domain}`);
    
    let cookiesData = null;
    
    // First try to get cookies from cache
    try {
      const cachedData = await getCachedUserData();
      if (cachedData && cachedData.sites && cachedData.cookies) {
        // Find site by domain in cached data
        const site = cachedData.sites.data?.find(s => s.domain === domain);
        if (site) {
          // Look for site cookies in the cookies folder structure
          const siteFileName = `site_${site.id}.json`;
          const siteSession = cachedData.cookies[siteFileName];
          
          if (siteSession && siteSession.cookies) {
            console.log(`Using cached cookies for domain: ${domain}`);
            cookiesData = {
              success: true,
              data: {
                cookies: siteSession.cookies
              }
            };
          }
        }
      }
    } catch (error) {
      console.log('Cache not available, falling back to API:', error.message);
    }
    
    // Fallback to API if not in cache
    if (!cookiesData) {
      console.log(`Fetching cookies from API for domain: ${domain}`);
      const response = await fetch(`${API_BASE_URL}/sites/cookies`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ domain: domain })
      });
      
      if (!response.ok) {
        console.log(`No cookies found for domain ${domain}`);
        return;
      }
      
      cookiesData = await response.json();
    }
    
    if (cookiesData && cookiesData.success && cookiesData.data && cookiesData.data.cookies && cookiesData.data.cookies.length > 0) {
      console.log(`🍪 Auto-injecting ${cookiesData.data.cookies.length} cookies for ${domain}`);
      
      // Set cookies one by one
      let successCount = 0;
      for (const cookie of cookiesData.data.cookies) {
        try {
          await setCookieBackground(cookie);
          successCount++;
          console.log(`✅ Auto-injected cookie: ${cookie.name}`);
        } catch (error) {
          console.warn(`❌ Failed to auto-inject cookie ${cookie.name}:`, error.message);
        }
      }
      
      if (successCount > 0) {
        console.log(`🎉 Successfully auto-injected ${successCount} cookies for ${domain}`);
        
        // Update badge to show success
        if (tabId) {
          chrome.action.setBadgeText({ text: successCount.toString(), tabId: tabId });
          chrome.action.setBadgeBackgroundColor({ color: "#10B981" });
          
          // Clear badge after 3 seconds
          setTimeout(() => {
            chrome.action.setBadgeText({ text: "", tabId: tabId });
          }, 3000);
        }
      }
    } else {
      console.log(`ℹ️ No cookies found for domain ${domain}`);
    }
    
    // Reset flag setelah operasi selesai
    isProcessingCookies = false;
    console.log('🔓 Auto-inject completed - flag cleared immediately');
  } catch (error) {
    console.log('❌ Auto-inject error:', error.message);
    // Reset flag jika terjadi error
    isProcessingCookies = false;
    console.log('🔓 Auto-inject error - flag cleared immediately');
  }
}

// Fungsi utility untuk validasi URL
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Event listener untuk perubahan cookies - ENABLED dengan perlindungan flag
// Cookie listener diaktifkan dengan perlindungan isProcessingCookies untuk mencegah looping
chrome.cookies.onChanged.addListener((changeInfo) => {
  // Ignore cookie changes during our own operations to prevent infinite loops
  if (isProcessingCookies) {
    return; // Silent ignore during processing
  }
  
  // Ignore tracking cookies that cause looping
  const cookieName = changeInfo.cookie.name;
  const ignoredCookies = [
    '_dd_s',
    'dd_cookie_test_',
    '__cf_bm',
    '_cfuvid',
    'cf_clearance'
  ];
  
  // Check if cookie should be ignored
  const shouldIgnore = ignoredCookies.some(ignored => 
    cookieName === ignored || cookieName.startsWith(ignored)
  );
  
  if (shouldIgnore) {
    return; // Silent ignore for tracking cookies
  }
  
  // Only log important cookie changes (not tracking cookies)
  if (changeInfo.removed) {
    console.log('🗑️ Important cookie removed:', cookieName, 'from', changeInfo.cookie.domain);
  } else {
    console.log('🍪 Important cookie added/updated:', cookieName, 'for', changeInfo.cookie.domain);
  }
});

// Fungsi untuk membersihkan cookies yang sudah expired dan domain cooldowns
function cleanupExpiredCookies() {
  chrome.cookies.getAll({}, (cookies) => {
    const now = Date.now() / 1000;
    let cleanedCount = 0;
    
    cookies.forEach((cookie) => {
      if (cookie.expirationDate && cookie.expirationDate < now) {
        chrome.cookies.remove({
          url: `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`,
          name: cookie.name
        }, (details) => {
          if (details) {
            cleanedCount++;
            console.log(`Cleaned expired cookie: ${cookie.name}`);
          }
        });
      }
    });
    
    if (cleanedCount > 0) {
      console.log(`Cleaned ${cleanedCount} expired cookies`);
    }
  });
  
  // Cleanup old domain cooldown entries (older than 1 hour)
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  let cleanedDomains = 0;
  
  for (const [domain, timestamp] of lastAutoInjectDomains.entries()) {
    if (timestamp < oneHourAgo) {
      lastAutoInjectDomains.delete(domain);
      cleanedDomains++;
    }
  }
  
  if (cleanedDomains > 0) {
    console.log(`Cleaned ${cleanedDomains} old domain cooldown entries`);
  }
}

// Jalankan cleanup setiap jam
setInterval(cleanupExpiredCookies, 60 * 60 * 1000);

// Jalankan cleanup saat extension dimuat
cleanupExpiredCookies();

// ===== CACHE MANAGEMENT FUNCTIONS =====

// Initialize cache on extension startup
async function initializeCache() {
  try {
    console.log('Initializing cache...');
    
    // Check if we have valid user session
    const userSession = await chrome.storage.local.get(CACHE_KEYS.USER_SESSION);
    if (userSession[CACHE_KEYS.USER_SESSION]) {
      console.log('Found user session:', userSession[CACHE_KEYS.USER_SESSION].sessionId);
      
      // Check if session is still valid
      const lastSync = await chrome.storage.local.get(CACHE_KEYS.LAST_SYNC);
      const now = Date.now();
      
      if (lastSync[CACHE_KEYS.LAST_SYNC] && (now - lastSync[CACHE_KEYS.LAST_SYNC]) < CACHE_DURATION) {
        console.log('Session is still valid');
        chrome.action.setBadgeText({ text: "✓" });
        chrome.action.setBadgeBackgroundColor({ color: "#10B981" });
        
        // Log session info
        const sessionsFolder = await chrome.storage.local.get(CACHE_KEYS.SESSIONS_FOLDER);
        if (sessionsFolder[CACHE_KEYS.SESSIONS_FOLDER]) {
          const cookiesFolder = sessionsFolder[CACHE_KEYS.SESSIONS_FOLDER][SESSION_STRUCTURE.COOKIES_FOLDER] || {};
          console.log(`Session loaded with ${Object.keys(cookiesFolder).length} cached cookie files`);
        }
      } else {
        console.log('Session expired, clearing...');
        await clearAllCachedData();
      }
    } else {
      console.log('No user session found');
    }
  } catch (error) {
    console.error('Error initializing cache:', error);
  }
}

// Download and cache user data after successful login
async function downloadAndCacheUserData(authData) {
  try {
    console.log('Downloading and caching user data...');
    console.log('Received authData:', authData);
    
    // Validate authData
    if (!authData) {
      throw new Error('authData is undefined or null');
    }
    
    if (!authData.user) {
      throw new Error('authData.user is undefined or null');
    }
    
    if (!authData.token) {
      throw new Error('authData.token is undefined or null');
    }
    
    // Create session structure
    const sessionId = `session_${Date.now()}`;
    const userSession = {
      sessionId: sessionId,
      user: authData.user,
      token: authData.token,
      loginTime: new Date().toISOString(),
      lastSync: Date.now()
    };
    
    // Store user session data
    await chrome.storage.local.set({
      [CACHE_KEYS.USER_SESSION]: userSession,
      [CACHE_KEYS.LAST_SYNC]: Date.now()
    });
    
    console.log('User session created:', sessionId);
    
    // Download sites data
    const sitesResponse = await fetch(`${API_BASE_URL}/sites`, {
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!sitesResponse.ok) {
      throw new Error(`Failed to fetch sites: ${sitesResponse.status}`);
    }
    
    const sitesData = await sitesResponse.json();
    
    // Store sites data
    await chrome.storage.local.set({
      [CACHE_KEYS.SITES_DATA]: sitesData
    });
    
    console.log(`Downloaded ${sitesData.data?.length || 0} sites`);
    
    // Create sessions folder structure
    const sessionsFolder = {};
    const cookiesFolder = {};
    
    // Download and cache cookies for each site
    for (const site of sitesData.data || []) {
      if (site.cookie_file_path) {
        try {
          console.log(`Downloading cookies for site: ${site.name}`);
          
          // Download cookie file using the new endpoint
          const cookieResponse = await fetch(`${API_BASE_URL}/sites/${site.id}/cookies`, {
            headers: {
              'Authorization': `Bearer ${authData.token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (cookieResponse.ok) {
            const cookieData = await cookieResponse.json();
            
            if (cookieData.success && cookieData.data) {
              // Store cookies with site info
              const siteSession = {
                siteId: site.id,
                siteName: site.name,
                domain: site.domain,
                cookies: cookieData.data.cookies || cookieData.data,
                downloadTime: new Date().toISOString(),
                cookieFileUploadedAt: cookieData.data.cookie_file_uploaded_at
              };
              
              cookiesFolder[`site_${site.id}.json`] = siteSession;
              console.log(`Successfully cached cookies for site: ${site.name} (${siteSession.cookies?.length || 0} cookies)`);
            } else {
              console.warn(`No cookies data for site: ${site.name}`);
            }
          } else {
            console.warn(`Failed to download cookies for site: ${site.name} (${cookieResponse.status})`);
          }
        } catch (error) {
          console.error(`Error downloading cookies for site ${site.name}:`, error);
        }
      } else {
        console.log(`No cookie file for site: ${site.name}`);
      }
    }
    
    // Store sessions folder structure
    sessionsFolder[SESSION_STRUCTURE.USER_FILE] = userSession;
    sessionsFolder[SESSION_STRUCTURE.COOKIES_FOLDER] = cookiesFolder;
    
    await chrome.storage.local.set({
      [CACHE_KEYS.SESSIONS_FOLDER]: sessionsFolder
    });
    
    console.log('User data and cookies cached successfully in structured folders');
    console.log('Session structure:', {
      userFile: SESSION_STRUCTURE.USER_FILE,
      cookiesCount: Object.keys(cookiesFolder).length,
      sessionId: sessionId
    });
    
  } catch (error) {
    console.error('Error downloading and caching user data:', error);
    throw error;
  }
}

// Get cached user data
async function getCachedUserData() {
  try {
    const result = await chrome.storage.local.get([
      CACHE_KEYS.USER_SESSION,
      CACHE_KEYS.SITES_DATA,
      CACHE_KEYS.SESSIONS_FOLDER,
      CACHE_KEYS.LAST_SYNC
    ]);
    
    // Check if cache is valid
    if (!await isCacheValid()) {
      return null;
    }
    
    const userSession = result[CACHE_KEYS.USER_SESSION];
    const sessionsFolder = result[CACHE_KEYS.SESSIONS_FOLDER];
    
    if (!userSession || !sessionsFolder) {
      return null;
    }
    
    return {
      userSession: userSession,
      sites: result[CACHE_KEYS.SITES_DATA],
      sessionsFolder: sessionsFolder,
      cookies: sessionsFolder[SESSION_STRUCTURE.COOKIES_FOLDER] || {},
      lastSync: result[CACHE_KEYS.LAST_SYNC]
    };
  } catch (error) {
    console.error('Error getting cached user data:', error);
    return null;
  }
}

// Clear all cached data (called on logout)
async function clearAllCachedData() {
  try {
    console.log('Clearing all cached data...');
    
    // Get current session info for logging
    const userSession = await chrome.storage.local.get(CACHE_KEYS.USER_SESSION);
    if (userSession[CACHE_KEYS.USER_SESSION]) {
      console.log('Clearing session:', userSession[CACHE_KEYS.USER_SESSION].sessionId);
    }
    
    await chrome.storage.local.remove([
      CACHE_KEYS.USER_SESSION,
      CACHE_KEYS.SITES_DATA,
      CACHE_KEYS.SESSIONS_FOLDER,
      CACHE_KEYS.LAST_SYNC
    ]);
    
    // Clear badge
    chrome.action.setBadgeText({ text: "" });
    
    console.log('All cached data and session cleared');
    
  } catch (error) {
    console.error('Error clearing cached data:', error);
    throw error;
  }
}

// Get cached cookies for a specific site
async function getCachedCookiesForSite(siteId) {
  try {
    const cachedData = await getCachedUserData();
    if (!cachedData || !cachedData.cookies) {
      return null;
    }
    
    const siteFileName = `site_${siteId}.json`;
    const siteSession = cachedData.cookies[siteFileName];
    
    if (siteSession && siteSession.cookies) {
      console.log(`Found cached cookies for site ${siteId}: ${siteSession.cookies.length} cookies`);
      return {
        success: true,
        data: {
          cookies: siteSession.cookies,
          site: {
            id: siteSession.siteId,
            name: siteSession.siteName,
            domain: siteSession.domain
          },
          downloadTime: siteSession.downloadTime,
          cookie_file_uploaded_at: siteSession.cookieFileUploadedAt
        }
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached cookies for site:', error);
    return null;
  }
}

// Check if cache is valid and fresh
async function isCacheValid() {
  try {
    const result = await chrome.storage.local.get([
      CACHE_KEYS.USER_SESSION,
      CACHE_KEYS.LAST_SYNC
    ]);
    
    const userSession = result[CACHE_KEYS.USER_SESSION];
    const lastSync = result[CACHE_KEYS.LAST_SYNC];
    
    if (!userSession || !lastSync) {
      return false;
    }
    
    const now = Date.now();
    return (now - lastSync) < CACHE_DURATION;
  } catch (error) {
    console.error('Error checking cache validity:', error);
    return false;
  }
}
