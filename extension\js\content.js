// Content script untuk Cookie Manager Extension
// Script ini berjalan di konteks halaman web

// Flag untuk mencegah multiple injection
if (!window.cookieManagerInjected) {
  window.cookieManagerInjected = true;
  
  // Listen for login success messages from web pages
  window.addEventListener('message', function(event) {
    console.log('Content script received message:', event.data);
    
    if (event.data && event.data.type === 'EXTENSION_LOGIN_SUCCESS') {
      console.log('Content script received login success message:', event.data);
      
      // Validate data before forwarding
      if (!event.data.token || !event.data.user) {
        console.error('Invalid login data received:', event.data);
        return;
      }
      
      // Forward the message to background script
      // Add a small delay to avoid conflicts with popup messages
      setTimeout(() => {
        try {
          chrome.runtime.sendMessage({
            action: 'loginSuccess',
            authData: {
              token: event.data.token,
              user: event.data.user
            },
            source: 'content_script'
          }, function(response) {
            console.log('Login success forwarded to background script:', response);
          });
        } catch (error) {
          console.error('Error forwarding login success to background:', error);
        }
      }, 100);
    }
  });



  // Event listener untuk pesan dari popup atau background
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "checkCookies") {
      // Gunakan Chrome API untuk mendapatkan semua cookies (termasuk httpOnly)
      try {
        chrome.runtime.sendMessage(
          { action: "getAllCookiesForDomain", domain: window.location.hostname },
          (response) => {
            if (chrome.runtime.lastError) {
              console.warn('Extension context error:', chrome.runtime.lastError.message);
              sendResponse({ 
                cookies: [], 
                domain: window.location.hostname,
                totalCount: 0,
                error: 'Extension context invalidated'
              });
              return;
            }
            sendResponse({ 
              cookies: response.cookies || [], 
              domain: window.location.hostname,
              totalCount: response.cookies ? response.cookies.length : 0
            });
          }
        );
      } catch (error) {
        console.warn('Runtime sendMessage error:', error);
        sendResponse({ 
          cookies: [], 
          domain: window.location.hostname,
          totalCount: 0,
          error: 'Extension context invalidated'
        });
      }
      return true; // Untuk async response
    }

    if (request.action === "injectCookieChecker") {
      // Inject cookie checker ke halaman dengan data yang akurat
      injectCookieCheckerWithRealData();
      sendResponse({ success: true });
    }

    if (request.action === "updateCookieDisplay") {
      // Update tampilan cookie count dengan data yang benar
      updateCookieDisplay(request.cookieCount);
      sendResponse({ success: true });
    }

    return true;
  });

  // Fungsi untuk inject cookie checker ke halaman (versi lama - hanya document.cookie)
  function injectCookieChecker() {
    // Buat element untuk menampilkan status cookies
    const cookieStatus = document.createElement("div");
    cookieStatus.id = "cookie-manager-status";
    cookieStatus.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;

    // Hitung jumlah cookies (hanya yang dapat diakses via document.cookie)
    const documentCookieCount = document.cookie
      .split(";")
      .filter((c) => c.trim()).length;
    
    cookieStatus.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">🍪 Cookie Manager</div>
            <div>Domain: ${window.location.hostname}</div>
            <div>Document Cookies: ${documentCookieCount}</div>
            <div style="color: yellow; font-size: 10px;">⚠️ Mengambil data lengkap...</div>
            <div style="margin-top: 5px; font-size: 10px; opacity: 0.7;">Extension aktif</div>
        `;

    // Tambahkan ke halaman
    document.body.appendChild(cookieStatus);

    // Auto-hide setelah 8 detik
    setTimeout(() => {
      if (cookieStatus.parentNode) {
        cookieStatus.parentNode.removeChild(cookieStatus);
      }
    }, 8000);
  }

  // Fungsi baru untuk inject cookie checker dengan data yang akurat
  function injectCookieCheckerWithRealData() {
    // Minta data cookie lengkap dari background script
    try {
      chrome.runtime.sendMessage(
        { action: "getAllCookiesForDomain", domain: window.location.hostname },
        (response) => {
          if (chrome.runtime.lastError) {
            console.warn('Extension context error in injectCookieChecker:', chrome.runtime.lastError.message);
            // Fallback ke document.cookie jika runtime error
            injectFallbackCookieChecker();
            return;
          }
          if (response && response.success) {
          const cookies = response.cookies || [];
          const documentCookies = document.cookie.split(";").filter((c) => c.trim()).length;
          const httpOnlyCookies = cookies.filter(c => c.httpOnly).length;
          const secureCookies = cookies.filter(c => c.secure).length;
          const sessionCookies = cookies.filter(c => c.session).length;
          
          // Analisis domain cookies
          const mainDomainCookies = cookies.filter(c => c.domain === window.location.hostname).length;
          const subDomainCookies = cookies.filter(c => c.domain.startsWith('.')).length;
          

          
          // Inject cookie checker dengan data yang akurat
          const script = document.createElement("script");
          script.textContent = `
            (function() {
              const cookieData = {
                total: ${cookies.length},
                httpOnly: ${httpOnlyCookies},
                documentAccessible: ${documentCookies},
                secure: ${secureCookies},
                session: ${sessionCookies},
                mainDomain: ${mainDomainCookies},
                subDomain: ${subDomainCookies},
                domain: '${window.location.hostname}'
              };
              
              
              
              // Update UI jika ada elemen cookie counter
              const cookieCounters = document.querySelectorAll('[data-cookie-count], .cookie-count, #cookie-count');
              cookieCounters.forEach(counter => {
                counter.textContent = \`\${cookieData.total} cookies (HttpOnly: \${cookieData.httpOnly}, Accessible: \${cookieData.documentAccessible})\`;
              });
              
              // Dispatch custom event dengan data cookie lengkap
              window.dispatchEvent(new CustomEvent('cookieDataUpdated', {
                detail: cookieData
              }));
              
              // Simpan data ke window untuk akses global
              window.cookieManagerData = cookieData;
            })();
          `;
          document.head.appendChild(script);
          document.head.removeChild(script);
        } else {
          // Fallback ke document.cookie jika API gagal
          const fallbackCount = document.cookie.split(";").filter((c) => c.trim()).length;
          
          // Inject fallback data
          const script = document.createElement("script");
          script.textContent = `
            (function() {
              const cookieData = {
                total: ${fallbackCount},
                httpOnly: 0,
                documentAccessible: ${fallbackCount},
                secure: 0,
                session: 0,
                mainDomain: ${fallbackCount},
                subDomain: 0,
                domain: '${window.location.hostname}'
              };
              
              
              window.cookieManagerData = cookieData;
            })();
          `;
          document.head.appendChild(script);
          document.head.removeChild(script);
        }
      }
    );
    } catch (error) {
      console.warn('Runtime sendMessage error in injectCookieChecker:', error);
      // Fallback ke document.cookie jika runtime error
      injectFallbackCookieChecker();
    }
  }

  // Fungsi fallback untuk inject cookie checker tanpa Chrome API
  function injectFallbackCookieChecker() {
    const fallbackCount = document.cookie.split(";").filter((c) => c.trim()).length;
    
    // Inject fallback data
    const script = document.createElement("script");
    script.textContent = `
      (function() {
        const cookieData = {
          total: ${fallbackCount},
          httpOnly: 0,
          documentAccessible: ${fallbackCount},
          secure: 0,
          session: 0,
          mainDomain: ${fallbackCount},
          subDomain: 0,
          domain: '${window.location.hostname}',
          fallback: true
        };
        
        // Update UI jika ada elemen cookie counter
        const cookieCounters = document.querySelectorAll('[data-cookie-count], .cookie-count, #cookie-count');
        cookieCounters.forEach(counter => {
          counter.textContent = \`\${cookieData.total} cookies (Fallback mode)\`;
        });
        
        // Dispatch custom event dengan data cookie lengkap
        window.dispatchEvent(new CustomEvent('cookieDataUpdated', {
          detail: cookieData
        }));
        
        // Simpan data ke window untuk akses global
        window.cookieManagerData = cookieData;
      })();
    `;
    document.head.appendChild(script);
    document.head.removeChild(script);
  }

  // Fungsi untuk update tampilan cookie count
  function updateCookieDisplay(cookieCount) {
    const existingStatus = document.getElementById("cookie-manager-status-real");
    if (existingStatus) {
      // Update konten yang sudah ada
      const totalElement = existingStatus.querySelector('[style*="color: #90EE90"]');
      if (totalElement) {
        totalElement.textContent = `✅ Total Cookies: ${cookieCount}`;
      }
    }
  }

  // Fungsi untuk monitoring perubahan cookies
  function monitorCookieChanges() {
    let lastCookieString = document.cookie;

    setInterval(() => {
      const currentCookieString = document.cookie;
      if (currentCookieString !== lastCookieString) {
        console.log("Cookie changed detected:", {
          domain: window.location.hostname,
          before: lastCookieString.split(";").length,
          after: currentCookieString.split(";").length,
        });

        // Kirim notifikasi ke background script
        chrome.runtime.sendMessage({
          action: "cookieChanged",
          domain: window.location.hostname,
          cookies: currentCookieString,
        });

        lastCookieString = currentCookieString;
      }
    }, 1000);
  }

  // Mulai monitoring (opsional)
  // monitorCookieChanges();

  // Event listener untuk DOM ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", onDOMReady);
  } else {
    onDOMReady();
  }

  function onDOMReady() {
    // Log informasi halaman dengan data yang akurat
    const documentCookieCount = document.cookie.split(";").filter((c) => c.trim()).length;
    
    // Dapatkan data cookie lengkap dari Chrome API
    chrome.runtime.sendMessage(
      { action: "getAllCookiesForDomain", domain: window.location.hostname },
      (response) => {
        const totalCookies = response.cookies ? response.cookies.length : 0;
        const httpOnlyCookies = response.cookies ? response.cookies.filter(c => c.httpOnly).length : 0;
      }
    );

    // Tambahkan event listener untuk form login (opsional)
    const loginForms = document.querySelectorAll(
      'form[action*="login"], form[action*="signin"], form[action*="auth"]'
    );
    loginForms.forEach((form) => {
      form.addEventListener("submit", () => {
        // Re-check cookies setelah form submit
        setTimeout(() => {
          try {
            chrome.runtime.sendMessage(
              { action: "getAllCookiesForDomain", domain: window.location.hostname },
              (response) => {
                if (chrome.runtime.lastError) {
                  console.warn('Extension context error in login monitor:', chrome.runtime.lastError.message);
                  return;
                }
                // Cookie count updated after login
              }
            );
          } catch (error) {
            console.warn('Runtime sendMessage error in login monitor:', error);
          }
        }, 2000);
      });
    });
  }

  // Utility function untuk mendapatkan semua cookies sebagai object
  function getAllCookiesAsObject() {
    const cookies = {};
    document.cookie.split(";").forEach((cookie) => {
      const [name, value] = cookie.trim().split("=");
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  // Expose utility functions ke window (untuk debugging)
  window.cookieManagerUtils = {
    getAllCookies: getAllCookiesAsObject,
    showStatus: injectCookieChecker,
    showAccurateStatus: injectCookieCheckerWithRealData,
    getDomain: () => window.location.hostname,
    getCookieCount: () =>
      document.cookie.split(";").filter((c) => c.trim()).length,
    getAccurateCookieCount: () => {
      return new Promise((resolve) => {
        try {
          chrome.runtime.sendMessage(
            { action: "getAllCookiesForDomain", domain: window.location.hostname },
            (response) => {
              if (chrome.runtime.lastError) {
                console.warn('Extension context error in getAccurateCookieCount:', chrome.runtime.lastError.message);
                // Fallback ke document.cookie count
                const fallbackCount = document.cookie.split(";").filter((c) => c.trim()).length;
                resolve(fallbackCount);
                return;
              }
              const count = response && response.success ? response.cookies.length : 0;
              resolve(count);
            }
          );
        } catch (error) {
          console.warn('Runtime sendMessage error in getAccurateCookieCount:', error);
          // Fallback ke document.cookie count
          const fallbackCount = document.cookie.split(";").filter((c) => c.trim()).length;
          resolve(fallbackCount);
        }
      });
    },
    monitorChanges: monitorCookieChanges
  };

  // Auto-inject untuk domain tertentu dengan data yang akurat
  const autoInjectDomains = ["chatgpt.com", "github.com", "stackoverflow.com"];
  if (autoInjectDomains.includes(window.location.hostname)) {
    setTimeout(() => {
      injectCookieCheckerWithRealData();
      // Juga jalankan monitoring cookie changes
      monitorCookieChanges();
    }, 2000);
  }
}
