<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contoh HTML untuk Encoder Tools</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #0066cc;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 5px;
    }
    .feature-list {
      list-style-type: square;
    }
    .highlight {
      background-color: #ffffcc;
      padding: 2px 5px;
      border-radius: 3px;
    }
    code {
      font-family: monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
    }
    .special-chars {
      font-weight: bold;
      color: #cc0000;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Encoder Tools</h1>
    <p>Ini adalah contoh file HTML yang dapat digunakan untuk menguji <span class="highlight">Encoder Tools</span>.</p>
    
    <h2>Fitur-fitur:</h2>
    <ul class="feature-list">
      <li>Encoding JSON</li>
      <li>Encoding HTML</li>
      <li>Encoding JavaScript</li>
      <li>Encoding File</li>
      <li>Encoding Folder</li>
    </ul>
    
    <h2>Contoh Penggunaan:</h2>
    <p>Untuk menggunakan Encoder Tools, jalankan perintah berikut:</p>
    <pre><code>node index.js encode-html "&lt;div&gt;Hello World&lt;/div&gt;"</code></pre>
    
    <h2>Karakter Khusus:</h2>
    <p class="special-chars">!@#$%^&*()_+{}[]|:;"'<>,.?/~`</p>
    
    <h2>Contoh Tag HTML:</h2>
    <div>
      <p>Paragraf <strong>tebal</strong> dan <em>miring</em>.</p>
      <a href="https://example.com">Link ke example.com</a>
      <br>
      <img src="image.jpg" alt="Gambar contoh" width="100">
      <table border="1">
        <tr>
          <th>Header 1</th>
          <th>Header 2</th>
        </tr>
        <tr>
          <td>Data 1</td>
          <td>Data 2</td>
        </tr>
      </table>
    </div>
    
    <h2>Script Embedded:</h2>
    <script>
      // Ini adalah contoh script JavaScript
      function showAlert() {
        alert("Hello from Encoder Tools!");
      }
      
      // Contoh objek JSON
      const config = {
        name: "Encoder Tools",
        version: "1.0.0",
        features: ["JSON", "HTML", "JavaScript"]
      };
      
      console.log("Config:", config);
    </script>
  </div>
</body>
</html>