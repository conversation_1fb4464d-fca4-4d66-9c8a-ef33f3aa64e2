<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Payment Link API - Midtrans</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .response-area {
            min-height: 200px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .alert-demo {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Demo Payment Link API</h1>
                
                @if(config('midtrans.server_key') === 'SB-Mid-server-YOUR_SERVER_KEY_HERE')
                <div class="alert alert-demo" role="alert">
                    <h5><i class="fas fa-info-circle"></i> Mode Demo Aktif</h5>
                    <p class="mb-0">Sistem Payment Link sedang dalam mode demo. Untuk menggunakan Midtrans yang sesungguhnya, silakan atur konfigurasi server key yang valid di file .env</p>
                </div>
                @endif

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Endpoint: POST /api/merchant-payment-order-001</h5>
                    </div>
                    <div class="card-body">
                        <form id="paymentLinkForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="order_id" class="form-label">Order ID *</label>
                                        <input type="text" class="form-control" id="order_id" name="order_id" value="ORDER-{{ time() }}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="gross_amount" class="form-label">Gross Amount *</label>
                                        <input type="number" class="form-control" id="gross_amount" name="gross_amount" value="150000" min="1000" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="payment_link_id" class="form-label">Payment Link ID</label>
                                        <input type="text" class="form-control" id="payment_link_id" name="payment_link_id" value="merchant-payment-order-{{ Str::random(8) }}">
                                    </div>
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title" name="title" value="Pembayaran Produk Demo" maxlength="40">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" value="John" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" value="Doe">
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone *</label>
                                        <input type="text" class="form-control" id="phone" name="phone" value="+6281234567890" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="usage_limit" class="form-label">Usage Limit</label>
                                        <input type="number" class="form-control" id="usage_limit" name="usage_limit" value="1" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="expiry_duration" class="form-label">Expiry Duration</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="expiry_duration" name="expiry_duration" value="24" min="1">
                                            <select class="form-select" id="expiry_unit" name="expiry_unit">
                                                <option value="minutes">Minutes</option>
                                                <option value="hours" selected>Hours</option>
                                                <option value="days">Days</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Enabled Payments</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="credit_card" id="payment_credit_card" checked>
                                            <label class="form-check-label" for="payment_credit_card">Credit Card</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="bca_va" id="payment_bca_va" checked>
                                            <label class="form-check-label" for="payment_bca_va">BCA Virtual Account</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="bni_va" id="payment_bni_va" checked>
                                            <label class="form-check-label" for="payment_bni_va">BNI Virtual Account</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="bri_va" id="payment_bri_va" checked>
                                            <label class="form-check-label" for="payment_bri_va">BRI Virtual Account</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="gopay" id="payment_gopay" checked>
                                            <label class="form-check-label" for="payment_gopay">GoPay</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="shopeepay" id="payment_shopeepay" checked>
                                            <label class="form-check-label" for="payment_shopeepay">ShopeePay</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="indomaret" id="payment_indomaret" checked>
                                            <label class="form-check-label" for="payment_indomaret">Indomaret</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="alfamart" id="payment_alfamart" checked>
                                            <label class="form-check-label" for="payment_alfamart">Alfamart</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">Buat Payment Link</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5>Response</h5>
                    </div>
                    <div class="card-body">
                        <div id="responseArea" class="response-area">
                            Response akan muncul di sini setelah form disubmit...
                        </div>
                        <div id="paymentLinkResult" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <h6>Payment Link berhasil dibuat!</h6>
                                <p class="mb-2">URL Payment Link:</p>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="paymentUrl" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">Copy</button>
                                    <button class="btn btn-success" type="button" onclick="openPaymentLink()">Buka Link</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Contoh Request cURL</h5>
                    </div>
                    <div class="card-body">
                        <div class="code-block">
curl -X POST {{ url('/api/merchant-payment-order-001') }} \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "order_id": "ORDER-123456789",
    "gross_amount": 150000,
    "payment_link_id": "merchant-payment-order-abc123",
    "customer_details": {
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "+6281234567890"
    },
    "item_details": [
      {
        "id": "item-001",
        "name": "Produk Demo",
        "price": 150000,
        "quantity": 1
      }
    ],
    "usage_limit": 1,
    "expiry_duration": 24,
    "expiry_unit": "hours",
    "enabled_payments": ["credit_card", "bca_va", "gopay"],
    "title": "Pembayaran Produk Demo"
  }'
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('paymentLinkForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const responseArea = document.getElementById('responseArea');
            const paymentLinkResult = document.getElementById('paymentLinkResult');
            
            // Reset hasil sebelumnya
            paymentLinkResult.style.display = 'none';
            responseArea.textContent = 'Mengirim request...';
            
            try {
                // Kumpulkan data form
                const formData = new FormData(this);
                
                // Kumpulkan enabled payments
                const enabledPayments = [];
                document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                    enabledPayments.push(checkbox.value);
                });
                
                // Siapkan payload
                const payload = {
                    order_id: formData.get('order_id'),
                    gross_amount: parseInt(formData.get('gross_amount')),
                    payment_link_id: formData.get('payment_link_id'),
                    customer_details: {
                        first_name: formData.get('first_name'),
                        last_name: formData.get('last_name'),
                        email: formData.get('email'),
                        phone: formData.get('phone')
                    },
                    item_details: [
                        {
                            id: 'item-001',
                            name: 'Produk Demo',
                            price: parseInt(formData.get('gross_amount')),
                            quantity: 1
                        }
                    ],
                    usage_limit: parseInt(formData.get('usage_limit')),
                    expiry_duration: parseInt(formData.get('expiry_duration')),
                    expiry_unit: formData.get('expiry_unit'),
                    enabled_payments: enabledPayments,
                    title: formData.get('title')
                };
                
                // Kirim request
                const response = await fetch('/api/merchant-payment-order-001', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify(payload)
                });
                
                const result = await response.json();
                
                // Tampilkan response
                responseArea.textContent = JSON.stringify(result, null, 2);
                
                // Jika berhasil, tampilkan link
                if (result.status === 'success' && result.data && result.data.payment_url) {
                    document.getElementById('paymentUrl').value = result.data.payment_url;
                    paymentLinkResult.style.display = 'block';
                }
                
            } catch (error) {
                responseArea.textContent = 'Error: ' + error.message;
            }
        });
        
        function copyToClipboard() {
            const paymentUrl = document.getElementById('paymentUrl');
            paymentUrl.select();
            document.execCommand('copy');
            alert('URL berhasil disalin ke clipboard!');
        }
        
        function openPaymentLink() {
            const paymentUrl = document.getElementById('paymentUrl').value;
            if (paymentUrl) {
                window.open(paymentUrl, '_blank');
            }
        }
    </script>
</body>
</html>