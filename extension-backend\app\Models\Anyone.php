<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Anyone extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\AnyoneFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'is_active',
        'subscription_plan',
        'subscription_status',
        'subscription_expires_at',
        'subscription_activated_at',
        'pending_order_id',
        'pending_payment_data',
        // Payment fields
        'payment_status',
        'midtrans_order_id',
        'midtrans_transaction_id',
        'payment_amount',
        'payment_date',
        'midtrans_response',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'subscription_expires_at' => 'datetime',
            'subscription_activated_at' => 'datetime',
            'pending_payment_data' => 'array',
            'payment_date' => 'datetime',
            'payment_amount' => 'decimal:2',
            'midtrans_response' => 'array',
        ];
    }

    /**
     * Check if user is elite
     */
    public function isElite(): bool
    {
        return $this->role === 'elite';
    }

    /**
     * Check if user is premium
     */
    public function isPremium(): bool
    {
        return $this->role === 'premium';
    }

    /**
     * Check if subscription is active (not expired)
     */
    public function isSubscriptionActive(): bool
    {
        if (!$this->subscription_expires_at) {
            return true; // No expiry date means unlimited access
        }
        
        return $this->subscription_expires_at->isFuture();
    }

    /**
     * Check if subscription is expired
     */
    public function isSubscriptionExpired(): bool
    {
        if (!$this->subscription_expires_at) {
            return false; // No expiry date means never expired
        }
        
        return $this->subscription_expires_at->isPast();
    }

    /**
     * Get days remaining until subscription expires
     */
    public function getDaysUntilExpiry(): ?int
    {
        if (!$this->subscription_expires_at) {
            return null; // Unlimited
        }
        
        return now()->diffInDays($this->subscription_expires_at, false);
    }
}
