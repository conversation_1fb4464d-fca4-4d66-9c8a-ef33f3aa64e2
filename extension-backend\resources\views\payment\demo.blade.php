<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo <PERSON></title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .payment-method-card {
            transition: all 0.3s ease;
        }
        .payment-method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .copy-button {
            transition: all 0.2s ease;
        }
        .copy-button:hover {
            transform: scale(1.05);
        }
        .success-animation {
            animation: successPulse 0.6s ease-in-out;
        }
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .countdown {
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <!-- Navigation -->
    <nav class="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <a href="{{ url('/') }}" class="flex items-center space-x-3">
                        <!-- Logo Satu Pintu -->
                        <svg width="40" height="40" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <circle cx="32" cy="32" r="30" fill="url(#doorGradient)"/>
                            <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="#ffffff" opacity="0.9"/>
                            <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradient)"/>
                            <text x="32" y="38" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#fbbf24">1</text>
                        </svg>
                        <span class="text-xl font-semibold">Satu Pintu</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="bg-yellow-500/20 border border-yellow-400/50 rounded-lg px-3 py-1">
                        <span class="text-yellow-400 text-sm font-semibold">
                            <i class="fas fa-flask mr-1"></i>Mode Demo
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-4">Pembayaran Demo</h1>
            <p class="text-xl text-gray-300">Simulasi proses pembayaran untuk paket berlangganan</p>
        </div>

        <!-- Payment Status -->
        <div class="bg-blue-500/20 border border-blue-400/50 rounded-2xl p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-bold mb-2">Status Pembayaran</h3>
                    <p class="text-blue-200">Order ID: <span class="font-mono">{{ $orderData['order_id'] ?? 'DEMO-' . time() }}</span></p>
                    <p class="text-blue-200">Jumlah: <span class="font-bold text-yellow-400">Rp {{ number_format($orderData['gross_amount'] ?? 49000, 0, ',', '.') }}</span></p>
                </div>
                <div class="text-center">
                    <div class="bg-yellow-500 text-black px-4 py-2 rounded-full font-bold mb-2">
                        <i class="fas fa-clock mr-2"></i>PENDING
                    </div>
                    <div class="countdown text-sm text-gray-300">
                        Auto sukses dalam: <span id="countdown">05:00</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="grid lg:grid-cols-2 gap-8">
            <!-- Bank Transfer -->
            <div class="payment-method-card bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <h3 class="text-xl font-bold mb-6 flex items-center">
                    <i class="fas fa-university text-blue-400 mr-3"></i>
                    Transfer Bank
                </h3>
                
                <div class="space-y-4">
                    <div class="bg-white/5 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Bank BCA</span>
                            <button onclick="copyToClipboard('**********')" class="copy-button bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                        <p class="text-gray-300">No. Rekening: <span class="font-mono text-yellow-400">**********</span></p>
                        <p class="text-gray-300">Atas Nama: <span class="font-semibold">PT Satu Pintu</span></p>
                    </div>
                    
                    <div class="bg-white/5 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Bank BNI</span>
                            <button onclick="copyToClipboard('**********')" class="copy-button bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                        <p class="text-gray-300">No. Rekening: <span class="font-mono text-yellow-400">**********</span></p>
                        <p class="text-gray-300">Atas Nama: <span class="font-semibold">PT Satu Pintu</span></p>
                    </div>
                    
                    <div class="bg-white/5 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Bank BRI</span>
                            <button onclick="copyToClipboard('**********')" class="copy-button bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                        <p class="text-gray-300">No. Rekening: <span class="font-mono text-yellow-400">**********</span></p>
                        <p class="text-gray-300">Atas Nama: <span class="font-semibold">PT Satu Pintu</span></p>
                    </div>
                    
                    <div class="bg-white/5 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">Bank Mandiri</span>
                            <button onclick="copyToClipboard('**********')" class="copy-button bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                        <p class="text-gray-300">No. Rekening: <span class="font-mono text-yellow-400">**********</span></p>
                        <p class="text-gray-300">Atas Nama: <span class="font-semibold">PT Satu Pintu</span></p>
                    </div>
                </div>
            </div>

            <!-- QRIS & Virtual Account -->
            <div class="space-y-6">
                <!-- QRIS -->
                <div class="payment-method-card bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-qrcode text-green-400 mr-3"></i>
                        QRIS
                    </h3>
                    
                    <div class="text-center">
                        <div class="bg-white p-4 rounded-lg inline-block mb-4">
                            <div class="w-48 h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                                <div class="text-white text-center">
                                    <i class="fas fa-qrcode text-6xl mb-2"></i>
                                    <p class="text-sm font-semibold">QR Code Demo</p>
                                    <p class="text-xs">Scan untuk bayar</p>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-300">Scan QR Code dengan aplikasi e-wallet Anda</p>
                        <p class="text-yellow-400 font-bold">Rp {{ number_format($orderData['gross_amount'] ?? 49000, 0, ',', '.') }}</p>
                    </div>
                </div>

                <!-- Virtual Account -->
                <div class="payment-method-card bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-xl font-bold mb-6 flex items-center">
                        <i class="fas fa-credit-card text-purple-400 mr-3"></i>
                        Virtual Account
                    </h3>
                    
                    <div class="bg-white/5 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">BCA Virtual Account</span>
                            <button onclick="copyToClipboard('{{ $vaNumber ?? '****************' }}')" class="copy-button bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                        <p class="text-gray-300">No. Virtual Account:</p>
                        <p class="font-mono text-yellow-400 text-lg font-bold">{{ $vaNumber ?? '****************' }}</p>
                        <p class="text-sm text-gray-400 mt-2">Gunakan nomor ini untuk transfer melalui ATM, mobile banking, atau internet banking BCA</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-yellow-500/20 border border-yellow-400/50 rounded-2xl p-6">
            <h3 class="text-xl font-bold mb-4 flex items-center">
                <i class="fas fa-info-circle text-yellow-400 mr-3"></i>
                Petunjuk Pembayaran Demo
            </h3>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold mb-3">Cara Pembayaran:</h4>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-400 mr-2 mt-1"></i>
                            Pilih salah satu metode pembayaran di atas
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-400 mr-2 mt-1"></i>
                            Salin nomor rekening atau scan QR Code
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-400 mr-2 mt-1"></i>
                            Lakukan pembayaran sesuai jumlah yang tertera
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-400 mr-2 mt-1"></i>
                            Tunggu konfirmasi pembayaran (otomatis dalam 5 menit)
                        </li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-3">Informasi Demo:</h4>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start">
                            <i class="fas fa-flask text-yellow-400 mr-2 mt-1"></i>
                            Ini adalah mode demo, tidak ada pembayaran nyata
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-clock text-blue-400 mr-2 mt-1"></i>
                            Pembayaran akan otomatis berhasil dalam 5 menit
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-user-plus text-green-400 mr-2 mt-1"></i>
                            Akun akan dibuat otomatis setelah pembayaran berhasil
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-purple-400 mr-2 mt-1"></i>
                            Notifikasi akan dikirim ke email yang didaftarkan
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-center space-x-4 mt-8">
            <button onclick="simulatePayment()" class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-all">
                <i class="fas fa-bolt mr-2"></i>Simulasi Pembayaran Berhasil
            </button>
            <a href="{{ url('/') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                <i class="fas fa-home mr-2"></i>Kembali ke Beranda
            </a>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 text-center max-w-md mx-4">
            <div class="success-animation">
                <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-white text-2xl"></i>
                </div>
            </div>
            <h3 class="text-2xl font-bold mb-4">Pembayaran Berhasil!</h3>
            <p class="text-gray-300 mb-6">Akun Anda telah dibuat dan diaktifkan. Selamat datang di Satu Pintu!</p>
            <div class="space-y-3">
                <button onclick="closeSuccessModal()" class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
                    Lanjutkan
                </button>
            </div>
        </div>
    </div>

    <script>
        let countdownTime = 300; // 5 minutes in seconds
        let countdownInterval;

        // Start countdown
        function startCountdown() {
            countdownInterval = setInterval(function() {
                const minutes = Math.floor(countdownTime / 60);
                const seconds = countdownTime % 60;
                
                document.getElementById('countdown').textContent = 
                    String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
                
                countdownTime--;
                
                if (countdownTime < 0) {
                    clearInterval(countdownInterval);
                    simulatePayment();
                }
            }, 1000);
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success feedback
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                button.classList.add('bg-green-600');
                
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.classList.remove('bg-green-600');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('Gagal menyalin: ' + text);
            });
        }

        // Simulate payment success
        async function simulatePayment() {
            try {
                clearInterval(countdownInterval);
                
                // Get user data from localStorage (set during registration)
                const userData = JSON.parse(localStorage.getItem('pendingUser') || '{}');
                
                if (!userData.id) {
                    throw new Error('User data not found');
                }

                // Call payment success API
                const response = await fetch('/api/payment/success', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        order_id: '{{ $orderData["order_id"] ?? "DEMO-" . time() }}',
                        user_id: userData.id,
                        payment_status: 'success'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Clear pending user data
                    localStorage.removeItem('pendingUser');
                    
                    // Show success modal
                    document.getElementById('successModal').classList.remove('hidden');
                    
                    // Update modal content with user info
                    const modalContent = document.querySelector('#successModal .bg-white\/10');
                    modalContent.innerHTML = `
                        <div class="success-animation">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-check text-white text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-2xl font-bold mb-4">Pembayaran Berhasil!</h3>
                        <p class="text-gray-300 mb-6">Akun ${result.user.name} telah diaktifkan dengan paket ${result.user.subscription_plan.toUpperCase()}. Selamat datang di Satu Pintu!</p>
                        <div class="space-y-3">
                            <a href="/login" class="block w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all text-center">
                                Login Sekarang
                            </a>
                            <button onclick="closeSuccessModal()" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                                Tutup
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(result.message || 'Payment processing failed');
                }
                
            } catch (error) {
                console.error('Payment simulation error:', error);
                
                // Show error in modal
                document.getElementById('successModal').classList.remove('hidden');
                const modalContent = document.querySelector('#successModal .bg-white\/10');
                modalContent.innerHTML = `
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-times text-white text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Error!</h3>
                    <p class="text-gray-300 mb-6">${error.message}</p>
                    <div class="space-y-3">
                        <button onclick="closeSuccessModal()" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all">
                            Tutup
                        </button>
                    </div>
                `;
            }
        }

        // Close success modal
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            window.location.href = '{{ url("/") }}';
        }

        // Initialize countdown when page loads
        document.addEventListener('DOMContentLoaded', function() {
            startCountdown();
        });
    </script>
</body>
</html>