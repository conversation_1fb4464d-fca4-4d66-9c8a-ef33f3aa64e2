<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\ImportantInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ImportantInfoController extends Controller
{
    /**
     * Display a listing of the important information.
     */
    public function index()
    {
        $importantInfos = ImportantInfo::orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'important_infos' => $importantInfos,
        ]);
    }

    /**
     * Store a newly created important information.
     */
    public function store(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:info,warning,error,success',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Create new important info
        $importantInfo = ImportantInfo::create([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'type' => $request->input('type'),
            'is_active' => $request->input('is_active', true),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Important information created successfully',
            'important_info' => $importantInfo,
        ], 201);
    }

    /**
     * Display the specified important information.
     */
    public function show($id)
    {
        $importantInfo = ImportantInfo::findOrFail($id);

        return response()->json([
            'success' => true,
            'important_info' => $importantInfo,
        ]);
    }

    /**
     * Update the specified important information.
     */
    public function update(Request $request, $id)
    {
        $importantInfo = ImportantInfo::findOrFail($id);

        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'content' => 'string',
            'type' => 'in:info,warning,error,success',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update important info
        $importantInfo->update($request->only([
            'title',
            'content',
            'type',
            'is_active',
            'start_date',
            'end_date',
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Important information updated successfully',
            'important_info' => $importantInfo,
        ]);
    }

    /**
     * Remove the specified important information.
     */
    public function destroy($id)
    {
        $importantInfo = ImportantInfo::findOrFail($id);
        $importantInfo->delete();

        return response()->json([
            'success' => true,
            'message' => 'Important information deleted successfully',
        ]);
    }

    /**
     * Get active important information.
     */
    public function getActive()
    {
        $importantInfo = ImportantInfo::active()
            ->orderBy('created_at', 'desc')
            ->first();

        return response()->json([
            'success' => true,
            'important_info' => $importantInfo,
        ]);
    }
}