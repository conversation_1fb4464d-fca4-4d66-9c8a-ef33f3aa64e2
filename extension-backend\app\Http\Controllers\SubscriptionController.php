<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\Anyone;

class SubscriptionController extends Controller
{
    /**
     * Daftar paket berlangganan yang tersedia
     */
    private function getSubscriptionPlans()
    {
        return [
            'elite' => [
                'name' => 'Elite',
                'price' => 29000,
                'features' => [
                    'Akses 50+ aplikasi premium',
                    'Sinkronisasi multi-device',
                    'Support email 24/7',
                    'Backup otomatis',
                    'Fitur dasar premium'
                ],
                'popular' => false
            ],
            'premium' => [
                'name' => 'Premium',
                'price' => 49000,
                'features' => [
                    'Akses aplikasi eksklusif unlimited',
                    'Tanpa limit penggunaan aplikasi',
                    'Semua fitur Elite',
                    'Fitur AI advanced',
                    'Priority support',
                    'Custom integrations'
                ],
                'popular' => true
            ]
        ];
    }

    /**
     * Daftar kode diskon yang tersedia
     */
    private function getDiscountCodes()
    {
        return [
            'WELCOME10' => [
                'discount_percent' => 10,
                'description' => 'Diskon 10% untuk pengguna baru',
                'min_amount' => 10000,
                'active' => true
            ],
            'PREMIUM20' => [
                'discount_percent' => 20,
                'description' => 'Diskon 20% khusus paket Premium',
                'min_amount' => 10000,
                'active' => true
            ],
            'GRATIS' => [
                'discount_percent' => 99,
                'description' => 'Diskon 100% khusus',
                'min_amount' => 10000,
                'active' => true
            ],
        ];
    }

    /**
     * Tampilkan halaman pemilihan paket
     */
    public function showPlans()
    {
        $plans = $this->getSubscriptionPlans();
        return view('subscription.plans', compact('plans'));
    }

    /**
     * Proses registrasi dan pemilihan paket
     */
    public function processRegistration(Request $request): JsonResponse
    {
        try {
            // Debug: Log request data
            Log::info('Registration request data:', $request->all());

            // Validasi input
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:anyones,email',
                'password' => 'required|string|min:8|confirmed',
                'plan' => 'required|string|in:elite,premium',
                'discount_code' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                Log::error('Validation failed:', $validator->errors()->toArray());
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 400);
            }

            $plans = $this->getSubscriptionPlans();
            $selectedPlan = $plans[$request->plan];

            if (!$selectedPlan) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Paket yang dipilih tidak valid'
                ], 400);
            }

            // Hitung harga dengan diskon
            $originalPrice = $selectedPlan['price'];
            $finalPrice = $originalPrice;
            $discountAmount = 0;
            $discountInfo = null;

            if ($request->discount_code) {
                $discountResult = $this->applyDiscount($request->discount_code, $originalPrice);
                if ($discountResult['valid']) {
                    $finalPrice = $discountResult['final_price'];
                    $discountAmount = $discountResult['discount_amount'];
                    $discountInfo = $discountResult['discount_info'];
                }
            }

            // Generate order ID tanpa membuat user dulu
            $orderId = 'SUB-' . time() . '-' . Str::random(8);

            // Simpan data pendaftaran sementara untuk dibuat setelah pembayaran berhasil
            $registrationData = [
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone ?? null,
                'plan' => $request->plan,
                'final_price' => $finalPrice,
                'discount_code' => $request->discount_code,
                'discount_amount' => $discountAmount,
                'order_id' => $orderId
            ];

            // Simpan data registrasi dalam cache untuk digunakan setelah pembayaran berhasil
            cache()->put('registration_' . $orderId, $registrationData, now()->addHours(24));

            // Konfigurasi Midtrans sesuai dokumentasi resmi
            $serverKey = config('midtrans.server_key');

            // Validasi server key
            if (empty($serverKey)) {
                throw new \Exception('Midtrans server key tidak ditemukan. Pastikan MIDTRANS_SERVER_KEY sudah diset di file .env');
            }

            // Debug: Log server key untuk memastikan format yang benar
            Log::info('Midtrans Server Key (first 15 chars): ' . substr($serverKey, 0, 15));

            // Validasi format server key berdasarkan environment
            $isProduction = config('midtrans.is_production', false);

            if ($isProduction) {
                // Production: server key harus dimulai dengan 'Mid-server-'
                if (!str_starts_with($serverKey, 'Mid-server-')) {
                    throw new \Exception('Server key tidak valid untuk production environment. Pastikan menggunakan server key yang dimulai dengan "Mid-server-"');
                }
            } else {
                // Sandbox: server key harus dimulai dengan 'SB-Mid-server-'
                if (!str_starts_with($serverKey, 'SB-Mid-server-')) {
                    throw new \Exception('Server key tidak valid untuk sandbox environment. Pastikan menggunakan server key yang dimulai dengan "SB-Mid-server-"');
                }
            }

            \Midtrans\Config::$serverKey = $serverKey;
            \Midtrans\Config::$isProduction = config('midtrans.is_production', false);
            \Midtrans\Config::$isSanitized = config('midtrans.is_sanitized', true);
            \Midtrans\Config::$is3ds = config('midtrans.is_3ds', true);

            // Debug: Log konfigurasi Midtrans
            Log::info('Midtrans Config', [
                'isProduction' => \Midtrans\Config::$isProduction,
                'isSanitized' => \Midtrans\Config::$isSanitized,
                'is3ds' => \Midtrans\Config::$is3ds,
                'expected_url' => \Midtrans\Config::$isProduction ? 'https://app.midtrans.com' : 'https://app.sandbox.midtrans.com'
            ]);

            // Parameter untuk Midtrans Snap
            $params = [
                'transaction_details' => [
                    'order_id' => $orderId,
                    'gross_amount' => $finalPrice,
                ],
                'customer_details' => [
                    'first_name' => $request->name,
                    'email' => $request->email,
                    'phone' => $request->phone ?? '+6281234567890'
                ],
                'item_details' => [[
                    'id' => $request->plan,
                    'price' => $finalPrice,
                    'quantity' => 1,
                    'name' => 'Berlangganan ' . $selectedPlan['name']
                ]],
                'callbacks' => [
                    'finish' => url('/subscription/payment-success')
                ]
            ];

            // Debug: Log parameter yang akan dikirim ke Midtrans
            Log::info('Midtrans Snap Parameters', $params);

            // Buat Snap Token dengan error handling yang lebih detail
            try {
                $snapToken = \Midtrans\Snap::getSnapToken($params);
                Log::info('Snap token berhasil dibuat: ' . substr($snapToken, 0, 20) . '...');
            } catch (\Exception $e) {
                Log::error('Error creating Snap token', [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'server_key_prefix' => substr($serverKey, 0, 10)
                ]);

                // Cek apakah ini error 401 dari Midtrans
                if (strpos($e->getMessage(), '401') !== false || strpos($e->getMessage(), 'unauthorized') !== false) {
                    throw new \Exception('Error autentikasi Midtrans. Pastikan MIDTRANS_SERVER_KEY benar dan sesuai environment (sandbox/production). Error: ' . $e->getMessage());
                }

                throw new \Exception('Gagal membuat token pembayaran: ' . $e->getMessage());
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Silakan lanjutkan pembayaran',
                'data' => [
                    'snap_token' => $snapToken,
                    'order_id' => $orderId,
                    'plan' => $selectedPlan,
                    'original_price' => $originalPrice,
                    'final_price' => $finalPrice,
                    'discount_amount' => $discountAmount,
                    'discount_info' => $discountInfo
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Registration failed: ' . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'Registrasi gagal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validasi dan terapkan kode diskon
     */
    private function applyDiscount($discountCode, $originalPrice)
    {
        $discountCodes = $this->getDiscountCodes();
        $code = strtoupper($discountCode);

        if (!isset($discountCodes[$code])) {
            return [
                'valid' => false,
                'message' => 'Kode diskon tidak valid'
            ];
        }

        $discount = $discountCodes[$code];

        if (!$discount['active']) {
            return [
                'valid' => false,
                'message' => 'Kode diskon sudah tidak aktif'
            ];
        }

        if ($originalPrice < $discount['min_amount']) {
            return [
                'valid' => false,
                'message' => 'Minimal pembelian Rp ' . number_format($discount['min_amount'], 0, ',', '.') . ' untuk menggunakan kode ini'
            ];
        }

        $discountAmount = ($originalPrice * $discount['discount_percent']) / 100;
        $finalPrice = $originalPrice - $discountAmount;

        return [
            'valid' => true,
            'discount_amount' => $discountAmount,
            'final_price' => $finalPrice,
            'discount_info' => $discount
        ];
    }

    /**
     * Dapatkan metode pembayaran yang diaktifkan berdasarkan pilihan
     */
    private function getEnabledPayments($paymentMethod)
    {
        $allPayments = [
            'credit_card', 'bca_va', 'bni_va', 'bri_va', 'mandiri_va',
            'gopay', 'shopeepay', 'dana', 'linkaja', 'ovo',
            'indomaret', 'alfamart'
        ];

        switch ($paymentMethod) {
            case 'bank_transfer':
                return ['bca_va', 'bni_va', 'bri_va', 'mandiri_va'];
            case 'e_wallet':
                return ['gopay', 'shopeepay', 'dana', 'linkaja', 'ovo'];
            case 'retail':
                return ['indomaret', 'alfamart'];
            case 'credit_card':
                return ['credit_card'];
            default:
                return $allPayments;
        }
    }

    /**
     * Validasi kode diskon via AJAX
     */
    public function validateDiscount(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'discount_code' => 'required|string',
            'amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Data tidak valid'
            ], 400);
        }

        $result = $this->applyDiscount($request->discount_code, $request->amount);

        if ($result['valid']) {
            return response()->json([
                'status' => 'success',
                'message' => 'Kode diskon valid',
                'data' => [
                    'discount_amount' => $result['discount_amount'],
                    'final_price' => $result['final_price'],
                    'discount_info' => $result['discount_info']
                ]
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * Handle Midtrans notification webhook
     */
    public function handleMidtransNotification(Request $request)
    {
        try {
            // Konfigurasi Midtrans sesuai dokumentasi resmi
            \Midtrans\Config::$serverKey = config('midtrans.server_key');
            \Midtrans\Config::$isProduction = config('midtrans.is_production', false);
            \Midtrans\Config::$isSanitized = config('midtrans.is_sanitized', true);
            \Midtrans\Config::$is3ds = config('midtrans.is_3ds', true);

            // Validasi server key
            if (empty(config('midtrans.server_key'))) {
                Log::error('Midtrans server key tidak ditemukan');
                return response()->json(['message' => 'Server configuration error'], 500);
            }

            $notification = new \Midtrans\Notification();

            $orderId = $notification->order_id;
            $transactionStatus = $notification->transaction_status;
            $fraudStatus = $notification->fraud_status;

            Log::info('Midtrans notification received', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'fraud_status' => $fraudStatus
            ]);

            // Ambil data registrasi dari cache
            $registrationData = cache()->get('registration_' . $orderId);

            if (!$registrationData) {
                Log::error('Registration data not found for order: ' . $orderId);
                return response()->json(['message' => 'Registration data not found'], 404);
            }

            if ($transactionStatus == 'capture' || $transactionStatus == 'settlement') {
                // Pembayaran berhasil, buat akun user
                $user = Anyone::create([
                    'name' => $registrationData['name'],
                    'email' => $registrationData['email'],
                    'password' => $registrationData['password'],
                    'phone' => $registrationData['phone'],
                    'role' => $registrationData['plan'],
                    'email_verified_at' => now(),
                    'subscription_plan' => $registrationData['plan'],
                    'subscription_status' => 'active',
                    'subscription_expires_at' => now()->addDays(30),
                    'subscription_activated_at' => now(),
                    'payment_status' => 'success',
                    'midtrans_order_id' => $orderId,
                    'payment_amount' => $registrationData['final_price'],
                    'payment_date' => now(),
                    'midtrans_response' => json_encode($notification->getResponse())
                ]);

                // Hapus data registrasi dari cache
                cache()->forget('registration_' . $orderId);

                Log::info('User account created successfully', [
                    'user_id' => $user->id,
                    'order_id' => $orderId,
                    'plan' => $user->subscription_plan
                ]);
            }

            return response()->json(['message' => 'Notification handled successfully']);

        } catch (\Exception $e) {
            Log::error('Midtrans notification handling failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process notification'
            ], 500);
        }
    }

    /**
     * Handle payment success page
     */
    public function handlePaymentSuccess(Request $request)
    {
        $orderId = $request->get('order_id');

        if ($orderId) {
            // Cek apakah user sudah dibuat
            $user = Anyone::where('midtrans_order_id', $orderId)->first();

            if ($user) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pembayaran berhasil! Akun Anda telah aktif.',
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'subscription_plan' => $user->subscription_plan,
                        'subscription_status' => $user->subscription_status
                    ]
                ]);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'Pembayaran sedang diproses, mohon tunggu beberapa saat.'
        ]);
    }
}
