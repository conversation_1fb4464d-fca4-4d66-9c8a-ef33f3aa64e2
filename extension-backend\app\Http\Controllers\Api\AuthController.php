<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Login pengguna elite/premium
     */
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid. Mohon periksa email dan password Anda.',
                'errors' => $e->errors()
            ], 422);
        }

        try {
            $user = Anyone::where('email', $request->email)
                         ->where('is_active', true)
                         ->first();

            if (!$user || !Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email atau password salah.',
                ], 401);
            }

            // Check if subscription is expired
            if ($user->isSubscriptionExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.',
                ], 403);
            }

            $token = $user->createToken('extension-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login berhasil',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription_expires_at' => $user->subscription_expires_at?->format('Y-m-d H:i:s'),
                    'subscription_active' => $user->isSubscriptionActive(),
                    'days_until_expiry' => $user->getDaysUntilExpiry(),
                ],
                'token' => $token,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan server. Silakan coba lagi.',
            ], 500);
        }
    }

    /**
     * Logout pengguna
     */
    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logout berhasil'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat logout.',
            ], 500);
        }
    }

    public function me(Request $request)
    {
        try {
            $user = $request->user();
            
            // Check if subscription is expired
            if ($user->isSubscriptionExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.',
                    'subscription_expired' => true,
                ], 403);
            }
            
            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription_expires_at' => $user->subscription_expires_at?->format('Y-m-d H:i:s'),
                    'subscription_active' => $user->isSubscriptionActive(),
                    'days_until_expiry' => $user->getDaysUntilExpiry(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat mengambil data pengguna.',
            ], 500);
        }
    }


    
    /**
     * Check if user is logged in from web and return token for extension
     */
    public function checkExtensionLogin(Request $request)
    {
        // Check if user is authenticated via web session
        if (Auth::guard('anyone')->check()) {
            $user = Auth::guard('anyone')->user();
            
            // Check if subscription is expired
            if ($user->isSubscriptionExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.',
                ], 403);
            }
            
            // Create token for extension
            $token = $user->createToken('extension-token')->plainTextToken;
            
            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'subscription_expires_at' => $user->subscription_expires_at?->format('Y-m-d H:i:s'),
                    'subscription_active' => $user->isSubscriptionActive(),
                    'days_until_expiry' => $user->getDaysUntilExpiry(),
                ],
                'token' => $token,
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Not authenticated'
        ], 401);
    }
    
    /**
     * Registrasi pengguna baru (opsional)
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:anyones',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:elite,premium',
        ]);

        $user = Anyone::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'is_active' => true,
        ]);

        $token = $user->createToken('extension-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Registrasi berhasil',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                ],
                'token' => $token,
            ]
        ], 201);
    }
}
