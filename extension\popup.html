<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>ie Manager Extension</title>
    <style>
        body {
            width: 420px;
            height: 600px;
            padding: 0;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            overflow: hidden;
            box-sizing: border-box;
        }
        .container {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 20px;
            padding: 24px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            height: calc(100% - 43px);
            margin: 16px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            overflow: hidden;
        }
        
        /* <PERSON><PERSON> Header Styles */
        .login-header {
            text-align: center;
            margin-bottom: 24px;
        }
        
        .app-logo {
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }
        
        .login-header h1 {
            margin: 8px 0;
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .login-header p {
            margin: 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }
        h1 {
            text-align: center;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
        }
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.1);
        }
        .upload-area.dragover {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        input[type="file"] {
            display: none;
        }
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px 0;
        }
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        .btn-primary:hover {
            background: #45a049;
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 14px;
            display: none;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #66bb6a;
            color: #81c784;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #ef5350;
            color: #e57373;
        }
        .status.warning {
            background: rgba(255, 152, 0, 0.3);
            border: 1px solid #ffb74d;
            color: #ffcc02;
        }
        .file-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            display: none;
        }
        .site-buttons {
            margin-top: 15px;
        }
        .site-btn {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .site-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .progress {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        /* Login Form Styles */
        .login-form {
            margin-top: 10px;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .input-group input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.15);
        }
        

        
        .troubleshooting {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
        }
        
        .troubleshooting h3 {
            margin: 0 0 10px 0;
            color: #ffc107;
            font-size: 14px;
        }
        
        .troubleshooting-content {
            font-size: 12px;
            line-height: 1.4;
        }
        
        .troubleshooting ol {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .troubleshooting li {
            margin-bottom: 4px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 11px;
            margin-top: 8px;
        }
        
        /* Main App Styles */
        .main-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            flex-shrink: 0;
        }
        
        .app-logo-main {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px auto;
            border-radius: 14px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #2563eb 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 24px rgba(79, 70, 229, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        .app-title {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .app-version {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 16px;
            font-weight: 500;
        }
        
        .user-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.1);
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 8px;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
        }
        
        .subscription-info {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }
        
        .subscription-info.warning {
            color: #fbbf24;
        }
        
        .subscription-info.danger {
            color: #f87171;
        }
        
        .role-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 4px;
            display: inline-block;
        }
        
        .role-badge.elite {
            background: rgba(59, 130, 246, 0.3);
            color: #60a5fa;
            border: 1px solid rgba(59, 130, 246, 0.5);
        }
        
        .role-badge.premium {
            background: rgba(34, 197, 94, 0.3);
            color: #4ade80;
            border: 1px solid rgba(34, 197, 94, 0.5);
        }
        
        .btn-logout {
            padding: 8px 16px;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.5);
            color: #f87171;
            border-radius: 8px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            flex-shrink: 0;
        }
        
        .btn-logout:hover {
            background: rgba(239, 68, 68, 0.3);
            transform: translateY(-1px);
        }
        
        /* Search Section */
        .search-section {
            margin-bottom: 16px;
            flex-shrink: 0;
        }
        
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 16px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .search-input:focus {
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            color: rgba(255, 255, 255, 0.6);
            pointer-events: none;
        }
        
        /* Filter Section */
        .filter-section {
            margin-bottom: 16px;
            flex-shrink: 0;
        }
        
        .filter-tabs {
            display: flex;
            gap: 6px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }
        
        .filter-tab {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .filter-tab.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .filter-tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Sites Section */
        .sites-section {
            flex: 1;
            min-height: 0;
            overflow-y: auto;
            padding-right: 8px;
            margin-top: 12px;
        }
        
        .sites-section::-webkit-scrollbar {
            width: 6px;
        }
        
        .sites-section::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        
        .sites-section::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        .sites-section::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        /* Sites Grid */
        .sites-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }
        
        .site-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 16px 12px 12px 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .site-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
        }
        
        .site-logo {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 24px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .site-logo img {
            width: 90%;
            height: 90%;
            object-fit: contain;
            border-radius: 12px;
        }
        
        .site-name {
            font-size: 11px;
            font-weight: 600;
            line-height: 1.2;
            color: rgba(255, 255, 255, 0.95);
            margin: 0;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            max-width: 100%;
        }
        
        .site-category-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 8px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .site-category-badge.elite {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a1a1a;
        }
        
        .site-category-badge.premium {
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            color: white;
        }
        
        .site-category-badge.general {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* No sites message */
        .no-sites {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            padding: 40px 20px;
            grid-column: 1 / -1;
            font-size: 14px;
        }
        
        .site-card.hidden {
            display: none;
        }
        
        /* Login Form Styles */
        .login-form {
            margin: 20px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: rgba(255, 255, 255, 0.15);
        }
        
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .btn-loading {
            display: none;
            align-items: center;
            gap: 8px;
        }
        
        .btn-loading .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Full page loader */
        .full-page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }
        
        .full-page-loader .loader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        .full-page-loader .loader-text {
            color: white;
            font-size: 14px;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .full-page-loader .loader-subtext {
            color: rgba(255,255,255,0.7);
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div class="container" id="loginScreen">
        <div class="login-header">
            <div class="app-logo">
                <svg width="48" height="48" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <linearGradient id="doorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
                      <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="numberGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                    </linearGradient>
                    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
                    </filter>
                  </defs>
                  
                  <!-- Background Circle -->
                  <circle cx="32" cy="32" r="30" fill="url(#doorGradient)" filter="url(#shadow)"/>
                  
                  <!-- Door Frame -->
                  <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="#ffffff" opacity="0.9"/>
                  <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradient)"/>
                  
                  <!-- Door Handle -->
                  <circle cx="38" cy="32" r="2" fill="#fbbf24"/>
                  <circle cx="38" cy="32" r="1.5" fill="#f59e0b"/>
                  
                  <!-- Door Panel Lines -->
                  <rect x="22" y="18" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                  <rect x="22" y="22" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                  <rect x="22" y="40" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                  <rect x="22" y="44" width="20" height="1" fill="rgba(255,255,255,0.3)"/>
                  
                  <!-- Number "1" -->
                  <g transform="translate(26, 26)">
                    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
                          stroke="url(#numberGradient)" 
                          stroke-width="3" 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          fill="none"/>
                    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
                          stroke="#ffffff" 
                          stroke-width="1.5" 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          fill="none"/>
                  </g>
                  
                  <!-- Decorative Elements -->
                  <circle cx="15" cy="15" r="1.5" fill="rgba(255,255,255,0.4)"/>
                  <circle cx="49" cy="15" r="1" fill="rgba(255,255,255,0.3)"/>
                  <circle cx="15" cy="49" r="1" fill="rgba(255,255,255,0.3)"/>
                  <circle cx="49" cy="49" r="1.5" fill="rgba(255,255,255,0.4)"/>
                </svg>
            </div>
            <h1>🚪 Satu Pintu</h1>
            <p>Akses Manager untuk Platform Elite & Premium</p>
        </div>
        
        <div class="login-form">
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" required placeholder="Masukkan email Anda">
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required placeholder="Masukkan password Anda">
                </div>
                
                <button type="submit" class="btn btn-primary" id="loginBtn">
                    <span class="btn-text">Masuk</span>
                    <span class="btn-loading" style="display: none;">
                        <div class="spinner"></div>
                        Memproses...
                    </span>
                </button>
            </form>
        </div>
        
        <div class="status" id="loginStatus"></div>
    </div>

    <!-- Main App Screen -->
    <div class="container" id="mainScreen" style="display: none;">
        <div class="main-header">
            <div class="app-logo-main">
                <svg width="48" height="48" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <linearGradient id="doorGradientMain" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
                      <stop offset="50%" style="stop-color:#f8fafc;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="numberGradientMain" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  
                  <!-- Door Frame -->
                  <rect x="18" y="12" width="28" height="40" rx="2" ry="2" fill="url(#doorGradientMain)" opacity="0.9"/>
                  <rect x="20" y="14" width="24" height="36" rx="1" ry="1" fill="url(#doorGradientMain)"/>
                  
                  <!-- Door Handle -->
                  <circle cx="38" cy="32" r="2" fill="#fbbf24"/>
                  <circle cx="38" cy="32" r="1.5" fill="#f59e0b"/>
                  
                  <!-- Door Panel Lines -->
                  <rect x="22" y="18" width="20" height="1" fill="rgba(79, 70, 229, 0.3)"/>
                  <rect x="22" y="22" width="20" height="1" fill="rgba(79, 70, 229, 0.3)"/>
                  <rect x="22" y="40" width="20" height="1" fill="rgba(79, 70, 229, 0.3)"/>
                  <rect x="22" y="44" width="20" height="1" fill="rgba(79, 70, 229, 0.3)"/>
                  
                  <!-- Number "1" -->
                  <g transform="translate(26, 26)">
                    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
                          stroke="url(#numberGradientMain)" 
                          stroke-width="3" 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          fill="none"/>
                    <path d="M8 2 L8 18 M5 5 L8 2 L8 18 M4 18 L12 18" 
                          stroke="#4f46e5" 
                          stroke-width="1.5" 
                          stroke-linecap="round" 
                          stroke-linejoin="round" 
                          fill="none"/>
                  </g>
                </svg>
            </div>
            <h1 class="app-title">🚪 Satu Pintu</h1>
            <div class="app-version" id="appVersion">Versi 3.0</div>
            
            <div class="user-section">
                <div class="user-info">
                    <span id="userName"></span>
                    <span id="userRole" class="role-badge"></span>
                    <div class="subscription-info" id="subscriptionInfo"></div>
                </div>
                <button class="btn-logout" id="logoutBtn">Keluar</button>
            </div>
        </div>
        
        <div class="search-section">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Cari aplikasi..." class="search-input">
                <div class="search-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="filter-section">
            <div class="filter-tabs" id="filterTabs">
                <button class="filter-tab active" data-category="all">Semua</button>
                <!-- Kategori akan dimuat secara dinamis -->
            </div>
        </div>
        
        <div class="sites-section" id="sitesSection">
            <div class="sites-grid" id="sitesList"></div>
        </div>
        
        <div class="status" id="status"></div>
        
        <!-- Troubleshooting Section -->
        <div class="troubleshooting" id="troubleshooting" style="display: none;">
            <h3>🔧 Troubleshooting</h3>
            <div class="troubleshooting-content">
                <p><strong>Jika ekstensi tidak berfungsi:</strong></p>
                <ol>
                    <li>Klik ikon ekstensi di toolbar browser</li>
                    <li>Klik "Reload" atau "Muat ulang"</li>
                    <li>Atau buka chrome://extensions/ dan klik reload</li>
                    <li>Coba login kembali</li>
                </ol>
                <button class="btn btn-small" onclick="location.reload()">Reload Popup</button>
            </div>
        </div>
    </div>
    
    <!-- Full Page Loader -->
    <div id="fullPageLoader" class="full-page-loader">
        <div class="loader-spinner"></div>
      <div class="loader-text">Memuat data situs...</div>
        <div class="loader-subtext">Mohon tunggu sebentar</div>
    </div>
    
    <script src="js/popup.js"></script>
</body>
</html>