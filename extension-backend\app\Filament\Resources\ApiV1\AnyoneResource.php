<?php

namespace App\Filament\Resources\ApiV1;

use App\Filament\Resources\ApiV1\AnyoneResource\Pages;
use App\Filament\Resources\ApiV1\AnyoneResource\RelationManagers;
use App\Models\Anyone;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AnyoneResource extends Resource
{
    protected static ?string $model = Anyone::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'Pengguna Elite/Premium';

    protected static ?string $modelLabel = 'Pengguna';

    protected static ?string $pluralModelLabel = 'Pengguna Elite/Premium';

    protected static ?string $navigationGroup = 'API V1';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),

                Forms\Components\TextInput::make('password')
                    ->label('Password')
                    ->password()
                    ->required(fn (string $context): bool => $context === 'create')
                    ->dehydrated(fn ($state) => filled($state))
                    ->minLength(8),

                Forms\Components\Select::make('role')
                    ->label('Role')
                    ->options([
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                    ])
                    ->required()
                    ->default('elite'),

                Forms\Components\Toggle::make('is_active')
                    ->label('Aktif')
                    ->default(true),

                Forms\Components\DateTimePicker::make('subscription_expires_at')
                    ->label('Masa Berakhir Langganan')
                    ->nullable()
                    ->helperText('Kosongkan untuk langganan unlimited'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('role')
                    ->label('Role')
                    ->colors([
                        'primary' => 'elite',
                        'success' => 'premium',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                        default => $state,
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('subscription_expires_at')
                    ->label('Masa Berakhir')
                    ->dateTime()
                    ->sortable()
                    ->placeholder('Unlimited')
                    ->color(fn ($record) => match (true) {
                        is_null($record->subscription_expires_at) => 'success',
                        $record->isSubscriptionExpired() => 'danger',
                        $record->getDaysUntilExpiry() <= 7 => 'warning',
                        default => 'primary'
                    }),

                Tables\Columns\TextColumn::make('subscription_status')
                    ->label('Status Langganan')
                    ->getStateUsing(function ($record) {
                        if (is_null($record->subscription_expires_at)) {
                            return 'Unlimited';
                        }
                        if ($record->isSubscriptionExpired()) {
                            return 'Berakhir';
                        }

                        if ($record->getDaysUntilExpiry() <= 7) {
                            return 'Akan Berakhir';
                        }
                        return 'Aktif';
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (is_null($record->subscription_expires_at)) {
                            return 'success';
                        }

                        if ($record->isSubscriptionExpired()) {
                            return 'danger';
                        }

                        if ($record->getDaysUntilExpiry() <= 7) {
                            return 'warning';
                        }

                        return 'primary';
                    }),

                Tables\Columns\TextColumn::make('email_verified_at')
                    ->label('Email Terverifikasi')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->label('Role')
                    ->options([
                        'elite' => 'Elite',
                        'premium' => 'Premium',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif'),

                Tables\Filters\Filter::make('email_verified')
                    ->label('Email Terverifikasi')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at')),

                Tables\Filters\SelectFilter::make('subscription_status')
                    ->label('Status Langganan')
                    ->options([
                        'active' => 'Aktif',
                        'expiring' => 'Akan Berakhir (≤7 hari)',
                        'expired' => 'Berakhir',
                        'unlimited' => 'Unlimited',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            function (Builder $query, $value): Builder {
                                return match ($value) {
                                    'active' => $query->where('subscription_expires_at', '>', now())
                                                      ->where('subscription_expires_at', '>', now()->addDays(7)),
                                    'expiring' => $query->where('subscription_expires_at', '>', now())
                                                        ->where('subscription_expires_at', '<=', now()->addDays(7)),
                                    'expired' => $query->where('subscription_expires_at', '<', now()),
                                    'unlimited' => $query->whereNull('subscription_expires_at'),
                                    default => $query,
                                };
                            }
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnyones::route('/'),
            'create' => Pages\CreateAnyone::route('/create'),
            'edit' => Pages\EditAnyone::route('/{record}/edit'),
        ];
    }
}
