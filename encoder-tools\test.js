/**
 * Test Script untuk Encoder Tools
 * 
 * Script ini berisi beberapa test case sederhana untuk memastikan
 * semua fungsi encoding ber<PERSON>lan dengan benar.
 */

const encoder = require('./encoder');
const fs = require('fs');
const path = require('path');

// Fungsi untuk menjalankan test
function runTests() {
  console.log('Menjalankan test untuk Encoder Tools...');
  console.log('============================================');
  
  // Test encoding JSON
  testEncodeJSON();
  
  // Test encoding HTML
  testEncodeHTML();
  
  // Test encoding JavaScript
  testEncodeJavaScript();
  
  // Test encoding file
  testEncodeFile();
  
  console.log('\nSemua test selesai!');
}

// Test encoding JSON
function testEncodeJSON() {
  console.log('\n[TEST] Encoding JSON');
  
  // Test case 1: JSON sederhana
  const jsonData1 = { name: '<PERSON>', age: 30 };
  const encodedJSON1 = encoder.encodeJSON(jsonData1, 'test_json1.txt');
  console.log('Test case 1 (JSON sederhana):', encodedJSON1 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 2: JSON dengan karakter khusus
  const jsonData2 = { name: 'John & Jane', message: '<Hello World>' };
  const encodedJSON2 = encoder.encodeJSON(jsonData2, 'test_json2.txt');
  console.log('Test case 2 (JSON dengan karakter khusus):', encodedJSON2 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 3: JSON string
  const jsonString = '{"name":"John","age":30}';
  const encodedJSON3 = encoder.encodeJSON(jsonString, 'test_json3.txt');
  console.log('Test case 3 (JSON string):', encodedJSON3 ? 'BERHASIL' : 'GAGAL');
}

// Test encoding HTML
function testEncodeHTML() {
  console.log('\n[TEST] Encoding HTML');
  
  // Test case 1: HTML sederhana
  const htmlData1 = '<div>Hello World</div>';
  const encodedHTML1 = encoder.encodeHTML(htmlData1, 'test_html1.txt');
  console.log('Test case 1 (HTML sederhana):', encodedHTML1 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 2: HTML dengan atribut
  const htmlData2 = '<a href="https://example.com" target="_blank">Link</a>';
  const encodedHTML2 = encoder.encodeHTML(htmlData2, 'test_html2.txt');
  console.log('Test case 2 (HTML dengan atribut):', encodedHTML2 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 3: HTML dengan karakter khusus
  const htmlData3 = '<div>Special chars: & < > " \'</div>';
  const encodedHTML3 = encoder.encodeHTML(htmlData3, 'test_html3.txt');
  console.log('Test case 3 (HTML dengan karakter khusus):', encodedHTML3 ? 'BERHASIL' : 'GAGAL');
}

// Test encoding JavaScript
function testEncodeJavaScript() {
  console.log('\n[TEST] Encoding JavaScript');
  
  // Test case 1: JavaScript sederhana
  const jsData1 = 'function test() { return true; }';
  const encodedJS1 = encoder.encodeJavaScript(jsData1, 'test_js1.txt');
  console.log('Test case 1 (JavaScript sederhana):', encodedJS1 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 2: JavaScript dengan karakter khusus
  const jsData2 = 'const message = "Hello & World"; console.log(message);';
  const encodedJS2 = encoder.encodeJavaScript(jsData2, 'test_js2.txt');
  console.log('Test case 2 (JavaScript dengan karakter khusus):', encodedJS2 ? 'BERHASIL' : 'GAGAL');
  
  // Test case 3: JavaScript dengan objek
  const jsData3 = 'const config = { url: "https://example.com", method: "GET" };';
  const encodedJS3 = encoder.encodeJavaScript(jsData3, 'test_js3.txt');
  console.log('Test case 3 (JavaScript dengan objek):', encodedJS3 ? 'BERHASIL' : 'GAGAL');
}

// Test encoding file
function testEncodeFile() {
  console.log('\n[TEST] Encoding File');
  
  // Buat file contoh untuk test
  const examplesDir = path.join(__dirname, 'examples');
  
  // Periksa apakah folder examples ada
  if (fs.existsSync(examplesDir)) {
    // Test case 1: File JSON
    const jsonFilePath = path.join(examplesDir, 'sample.json');
    if (fs.existsSync(jsonFilePath)) {
      try {
        const encodedFile1 = encoder.encodeFile(jsonFilePath);
        console.log('Test case 1 (File JSON):', encodedFile1 ? 'BERHASIL' : 'GAGAL');
      } catch (error) {
        console.error('Test case 1 (File JSON): GAGAL -', error.message);
      }
    } else {
      console.log('Test case 1 (File JSON): DILEWATI - File tidak ditemukan');
    }
    
    // Test case 2: File HTML
    const htmlFilePath = path.join(examplesDir, 'sample.html');
    if (fs.existsSync(htmlFilePath)) {
      try {
        const encodedFile2 = encoder.encodeFile(htmlFilePath);
        console.log('Test case 2 (File HTML):', encodedFile2 ? 'BERHASIL' : 'GAGAL');
      } catch (error) {
        console.error('Test case 2 (File HTML): GAGAL -', error.message);
      }
    } else {
      console.log('Test case 2 (File HTML): DILEWATI - File tidak ditemukan');
    }
    
    // Test case 3: File JavaScript
    const jsFilePath = path.join(examplesDir, 'sample.js');
    if (fs.existsSync(jsFilePath)) {
      try {
        const encodedFile3 = encoder.encodeFile(jsFilePath);
        console.log('Test case 3 (File JavaScript):', encodedFile3 ? 'BERHASIL' : 'GAGAL');
      } catch (error) {
        console.error('Test case 3 (File JavaScript): GAGAL -', error.message);
      }
    } else {
      console.log('Test case 3 (File JavaScript): DILEWATI - File tidak ditemukan');
    }
  } else {
    console.log('Test encoding file: DILEWATI - Folder examples tidak ditemukan');
  }
}

// Jalankan test
runTests();