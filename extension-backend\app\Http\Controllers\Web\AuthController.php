<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Anyone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * <PERSON><PERSON><PERSON><PERSON> halaman login
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * Proses login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = Anyone::where('email', $request->email)
                     ->where('is_active', true)
                     ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['Email atau password salah.'],
            ]);
        }

        // Login user menggunakan guard khusus untuk Anyone
        Auth::guard('anyone')->login($user);

        // Check if this is from extension
        if ($request->has('extension')) {
            // Create token for extension
            $token = $user->createToken('extension-token')->plainTextToken;

            // Redirect to extension with token and user data
            $extensionUrl = 'chrome-extension://lhjmdmckadnioglkhlfhcegaboaefhah/popup.html?' . http_build_query([
                'token' => $token,
                'user' => json_encode([
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                ])
            ]);

            // For now, redirect to a success page that will close the tab
            return view('auth.extension-success', [
                'token' => $token,
                'user' => $user
            ]);
        }

        // Regular web login
        return redirect()->route('dashboard');
    }

    /**
     * Logout
     */
    public function logout(Request $request)
    {
        Auth::guard('anyone')->logout();

        return redirect()->route('login')->with('success', 'Berhasil logout');
    }

    /**
     * Dashboard setelah login
     */
    public function dashboard()
    {
        $user = Auth::guard('anyone')->user();

        if (!$user) {
            return redirect()->route('login');
        }

        return view('dashboard', compact('user'));
    }
}
